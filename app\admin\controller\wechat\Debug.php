<?php
namespace app\admin\controller\wechat;

use app\common\basics\AdminBase;
use app\common\server\JsonServer;
use app\common\model\wechat\WechatReply;
use app\api\logic\WechatLogic;
use app\common\server\WeChatServer;
use EasyWeChat\Factory;
use EasyWeChat\Kernel\Messages\Image;

class Debug extends AdminBase
{
    /**
     * 调试进采购群功能
     */
    public function purchaseGroupDebug()
    {
        if ($this->request->isAjax()) {
            $action = $this->request->post('action');
            
            switch ($action) {
                case 'check_reply_rule':
                    return $this->checkReplyRule();
                case 'test_image_upload':
                    return $this->testImageUpload();
                case 'test_send_message':
                    return $this->testSendMessage();
                case 'check_wechat_config':
                    return $this->checkWechatConfig();
                default:
                    return JsonServer::error('未知操作');
            }
        }
        
        return view('debug');
    }
    
    /**
     * 检查回复规则
     */
    private function checkReplyRule()
    {
        try {
            $reply = WechatReply::where([
                'keyword' => '进采购群',
                'del' => 0
            ])->find();
            
            if (!$reply) {
                return JsonServer::error('未找到进采购群回复规则');
            }
            
            $data = $reply->toArray();
            
            // 检查配置
            $checks = [
                'content_type' => $data['content_type'] == 3 ? '✓' : '✗',
                'message_count' => $data['message_count'] == 2 ? '✓' : '✗',
                'second_content_type' => $data['second_content_type'] == 2 ? '✓' : '✗',
                'status' => $data['status'] == 1 ? '✓' : '✗',
                'second_image_url' => !empty($data['second_image_url']) ? '✓' : '✗'
            ];
            
            return JsonServer::success('检查完成', [
                'data' => $data,
                'checks' => $checks
            ]);
        } catch (\Exception $e) {
            return JsonServer::error('检查失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 测试图片上传
     */
    private function testImageUpload()
    {
        try {
            $reply = WechatReply::where([
                'keyword' => '进采购群',
                'del' => 0
            ])->find();
            
            if (!$reply || empty($reply['second_image_url'])) {
                return JsonServer::error('未找到图片URL配置');
            }
            
            $image_url = $reply['second_image_url'];
            $image_path = app()->getRootPath() . 'public' . $image_url;
            
            // 检查文件是否存在
            if (!file_exists($image_path)) {
                return JsonServer::error('图片文件不存在: ' . $image_path);
            }
            
            // 检查文件大小
            $fileSize = filesize($image_path);
            if ($fileSize > 2 * 1024 * 1024) {
                return JsonServer::error('图片文件过大: ' . $fileSize . ' bytes');
            }
            
            // 尝试上传到微信
            $media_id = WechatLogic::uploadImageToWechat($image_url);
            
            if ($media_id) {
                return JsonServer::success('图片上传成功', [
                    'media_id' => $media_id,
                    'file_size' => $fileSize,
                    'file_path' => $image_path
                ]);
            } else {
                return JsonServer::error('图片上传失败，请检查日志');
            }
        } catch (\Exception $e) {
            return JsonServer::error('测试失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 测试发送消息
     */
    private function testSendMessage()
    {
        try {
            $openid = $this->request->post('openid');
            if (empty($openid)) {
                return JsonServer::error('请输入测试用户的openid');
            }
            
            $reply = WechatReply::where([
                'keyword' => '进采购群',
                'del' => 0
            ])->find();
            
            if (!$reply) {
                return JsonServer::error('未找到回复规则');
            }
            
            // 测试发送第二条消息
            WechatLogic::sendSecondMessage($reply->toArray(), $openid);
            
            return JsonServer::success('测试消息已发送，请检查微信和日志');
        } catch (\Exception $e) {
            return JsonServer::error('发送失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 检查微信配置
     */
    private function checkWechatConfig()
    {
        try {
            $config = WeChatServer::getOaConfig();
            
            $checks = [
                'app_id' => !empty($config['app_id']) ? '✓' : '✗',
                'secret' => !empty($config['secret']) ? '✓' : '✗',
                'token' => !empty($config['token']) ? '✓' : '✗'
            ];
            
            // 测试获取access_token
            try {
                $app = Factory::officialAccount($config);
                $accessToken = $app->access_token->getToken();
                $checks['access_token'] = !empty($accessToken['access_token']) ? '✓' : '✗';
            } catch (\Exception $e) {
                $checks['access_token'] = '✗ (' . $e->getMessage() . ')';
            }
            
            return JsonServer::success('检查完成', [
                'config' => [
                    'app_id' => $config['app_id'] ?? '',
                    'secret' => !empty($config['secret']) ? '已配置' : '未配置',
                    'token' => $config['token'] ?? ''
                ],
                'checks' => $checks
            ]);
        } catch (\Exception $e) {
            return JsonServer::error('检查失败: ' . $e->getMessage());
        }
    }
}
