# 微信公众号进采购群功能部署说明

## 功能概述

本功能实现了微信公众号自定义菜单的"进采购群"功能，用户点击菜单或发送关键词后，系统会自动发送两条消息：
1. 第一条：欢迎文本消息
2. 第二条：采购群二维码图片

## 部署步骤

### 1. 数据库更新

执行以下SQL文件来更新数据库结构：

```bash
# 1. 扩展微信回复表结构
mysql -u用户名 -p数据库名 < database/migrations/20241228_extend_wechat_reply_table.sql

# 2. 插入进采购群回复规则
mysql -u用户名 -p数据库名 < database/migrations/20241228_insert_purchase_group_reply.sql
```

### 2. 上传二维码图片

1. 准备采购群二维码图片（建议300x300像素，JPG/PNG格式，不超过2MB）
2. 上传到 `public/uploads/qrcode/` 目录
3. 重命名为 `purchase_group_qr.jpg`

### 3. 配置微信公众号

#### 3.1 基本配置
1. 登录微信公众平台
2. 进入"开发-基本配置"
3. 确保服务器配置正确：
   - URL: `https://你的域名/api/wechat/index`
   - Token: 与系统配置一致
   - 消息加解密方式：根据需要选择

#### 3.2 菜单配置
1. 进入"自定义菜单"
2. 创建或编辑菜单
3. 添加菜单项：
   - 菜单名称：进采购群
   - 菜单类型：关键字
   - 关键字：进采购群
4. 发布菜单

### 4. 后台配置

#### 4.1 访问配置页面
访问：`https://你的域名/admin/wechat.oa/purchaseGroup`

#### 4.2 配置内容
1. 欢迎文本：自定义欢迎消息内容
2. 二维码图片：上传或选择二维码图片
3. 启用状态：设置为"启用"
4. 点击"保存配置"

### 5. 测试验证

#### 5.1 自动测试
访问测试页面：`https://你的域名/test_wechat_reply.html`

#### 5.2 手动测试
1. 使用测试账号关注公众号
2. 点击"进采购群"菜单
3. 验证是否收到两条消息
4. 直接发送"进采购群"文字测试

## 文件结构

```
kshop/
├── app/
│   ├── admin/
│   │   ├── controller/wechat/Oa.php          # 新增方法
│   │   ├── logic/wechat/ReplyLogic.php       # 修改回复逻辑
│   │   └── view/wechat/
│   │       ├── oa/purchase_group.html        # 新增配置页面
│   │       └── reply/add_text.html           # 修改支持多媒体
│   ├── api/logic/WechatLogic.php             # 修改消息处理逻辑
│   └── common/model/wechat/Wechat.php        # 新增常量定义
├── database/migrations/
│   ├── 20241228_extend_wechat_reply_table.sql
│   └── 20241228_insert_purchase_group_reply.sql
├── public/
│   ├── uploads/qrcode/                       # 二维码图片目录
│   └── test_wechat_reply.html               # 测试页面
└── docs/
    └── wechat_purchase_group_deployment.md  # 本文档
```

## 主要修改内容

### 1. 数据表扩展
- 新增 `image_url` 字段：存储图片URL
- 新增 `message_count` 字段：消息数量
- 新增 `second_content` 字段：第二条消息内容
- 新增 `second_content_type` 字段：第二条消息类型
- 新增 `second_image_url` 字段：第二条消息图片URL

### 2. 消息处理逻辑
- 支持多条消息发送
- 支持图片消息类型
- 异步发送第二条消息

### 3. 后台管理
- 新增多媒体回复配置界面
- 支持图片上传和预览
- 专门的进采购群配置页面

## 注意事项

1. **图片上传**：确保图片格式正确，大小合适
2. **微信限制**：微信对图片有大小和格式限制
3. **服务器配置**：确保服务器支持图片上传和存储
4. **权限设置**：确保上传目录有写入权限
5. **HTTPS要求**：微信公众号要求使用HTTPS

## 故障排除

### 常见问题

1. **收不到回复**
   - 检查回复规则是否启用
   - 确认关键词匹配正确
   - 验证微信配置是否正确

2. **图片显示失败**
   - 检查图片路径是否正确
   - 确认文件是否存在
   - 验证图片格式和大小

3. **只收到一条消息**
   - 检查 `message_count` 字段值
   - 确认第二条消息配置正确
   - 查看错误日志

4. **菜单点击无效**
   - 确认菜单已发布
   - 检查关键词配置
   - 验证服务器URL配置

### 日志查看

查看微信相关日志：
```bash
tail -f runtime/wechat/当前年月/当前日期.log
```

## 联系支持

如遇到问题，请提供以下信息：
1. 错误日志内容
2. 微信公众号配置截图
3. 数据库配置信息
4. 具体的错误现象描述
