<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>采购套餐购买测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .package-item {
            border: 1px solid #ddd;
            border-radius: 6px;
            padding: 15px;
            margin: 10px 0;
            background: #fafafa;
        }
        .package-item.selected {
            border-color: #007cba;
            background: #e7f3ff;
        }
        .btn {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #005a87;
        }
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .form-group {
            margin: 15px 0;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-group input, .form-group select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .order-info {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
        }
        .error {
            color: #d32f2f;
            background: #ffebee;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success {
            color: #2e7d32;
            background: #e8f5e8;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>采购套餐购买测试</h1>
        
        <!-- 套餐列表 -->
        <div id="packageList">
            <h2>选择套餐</h2>
            <div id="packages"></div>
        </div>
        
        <!-- 购买表单 -->
        <div id="purchaseForm" style="display: none;">
            <h2>购买信息</h2>
            <div class="form-group">
                <label>商家ID:</label>
                <input type="number" id="shopId" value="1" placeholder="请输入商家ID">
            </div>
            <div class="form-group">
                <label>支付方式:</label>
                <select id="payWay">
                    <option value="1">微信支付</option>
                    <option value="2">支付宝支付</option>
                </select>
            </div>
            <button class="btn" onclick="createOrder()">创建订单</button>
        </div>
        
        <!-- 订单信息 -->
        <div id="orderInfo" style="display: none;">
            <h2>订单信息</h2>
            <div id="orderDetails" class="order-info"></div>
            <button class="btn" onclick="payOrder()">立即支付</button>
        </div>
        
        <!-- 消息显示 -->
        <div id="message"></div>
    </div>

    <script>
        let selectedPackage = null;
        let currentOrder = null;
        
        // 显示消息
        function showMessage(message, type = 'info') {
            const messageDiv = document.getElementById('message');
            messageDiv.className = type;
            messageDiv.textContent = message;
            setTimeout(() => {
                messageDiv.textContent = '';
                messageDiv.className = '';
            }, 5000);
        }
        
        // 加载套餐列表
        async function loadPackages() {
            try {
                const response = await fetch('/shopapi/purchaser_package/packageList', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer your-token-here' // 需要替换为实际的token
                    }
                });
                
                const result = await response.json();
                
                if (result.code === 1) {
                    displayPackages(result.data);
                } else {
                    showMessage('加载套餐失败: ' + result.msg, 'error');
                }
            } catch (error) {
                showMessage('网络错误: ' + error.message, 'error');
            }
        }
        
        // 显示套餐列表
        function displayPackages(packages) {
            const packagesDiv = document.getElementById('packages');
            packagesDiv.innerHTML = '';
            
            packages.forEach(pkg => {
                const packageDiv = document.createElement('div');
                packageDiv.className = 'package-item';
                packageDiv.onclick = () => selectPackage(pkg, packageDiv);
                
                packageDiv.innerHTML = `
                    <h3>${pkg.name}</h3>
                    <p>分配人数: ${pkg.purchaser_count}人</p>
                    <p>价格: ¥${pkg.price}</p>
                `;
                
                packagesDiv.appendChild(packageDiv);
            });
        }
        
        // 选择套餐
        function selectPackage(pkg, element) {
            // 清除之前的选择
            document.querySelectorAll('.package-item').forEach(item => {
                item.classList.remove('selected');
            });
            
            // 选择当前套餐
            element.classList.add('selected');
            selectedPackage = pkg;
            
            // 显示购买表单
            document.getElementById('purchaseForm').style.display = 'block';
        }
        
        // 创建订单
        async function createOrder() {
            if (!selectedPackage) {
                showMessage('请先选择套餐', 'error');
                return;
            }
            
            const shopId = document.getElementById('shopId').value;
            if (!shopId) {
                showMessage('请输入商家ID', 'error');
                return;
            }
            
            try {
                const response = await fetch('/shopapi/purchaser_package/createOrder', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer your-token-here' // 需要替换为实际的token
                    },
                    body: JSON.stringify({
                        package_id: selectedPackage.id,
                        shop_id: parseInt(shopId)
                    })
                });
                
                const result = await response.json();
                
                if (result.code === 1) {
                    currentOrder = result.data;
                    displayOrderInfo(result.data);
                    showMessage('订单创建成功', 'success');
                } else {
                    showMessage('创建订单失败: ' + result.msg, 'error');
                }
            } catch (error) {
                showMessage('网络错误: ' + error.message, 'error');
            }
        }
        
        // 显示订单信息
        function displayOrderInfo(order) {
            const orderDetailsDiv = document.getElementById('orderDetails');
            orderDetailsDiv.innerHTML = `
                <p><strong>订单号:</strong> ${order.order_sn}</p>
                <p><strong>套餐名称:</strong> ${order.package_name}</p>
                <p><strong>分配人数:</strong> ${order.purchaser_count}人</p>
                <p><strong>支付金额:</strong> ¥${order.price}</p>
            `;
            
            document.getElementById('orderInfo').style.display = 'block';
        }
        
        // 支付订单
        async function payOrder() {
            if (!currentOrder) {
                showMessage('没有可支付的订单', 'error');
                return;
            }
            
            const payWay = document.getElementById('payWay').value;
            
            try {
                const response = await fetch('/shopapi/purchaser_package/payOrder', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer your-token-here' // 需要替换为实际的token
                    },
                    body: JSON.stringify({
                        order_sn: currentOrder.order_sn,
                        pay_way: parseInt(payWay)
                    })
                });
                
                const result = await response.json();
                
                if (result.code === 1 || result.code === 10001) {
                    showMessage('支付发起成功，请完成支付', 'success');
                    console.log('支付数据:', result.data);
                    // 这里可以根据支付方式调用相应的支付SDK
                } else {
                    showMessage('支付发起失败: ' + result.msg, 'error');
                }
            } catch (error) {
                showMessage('网络错误: ' + error.message, 'error');
            }
        }
        
        // 页面加载时获取套餐列表
        document.addEventListener('DOMContentLoaded', function() {
            loadPackages();
        });
    </script>
</body>
</html>
