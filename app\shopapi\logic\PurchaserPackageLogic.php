<?php
namespace app\shopapi\logic;

use app\common\basics\Logic;
use app\common\model\PurchaserPackage;
use app\common\model\PurchaserPackageOrder;
use app\common\server\JsonServer;
use think\facade\Db;

/**
 * 采购套餐购买逻辑
 * Class PurchaserPackageLogic
 * @package app\shopapi\logic
 */
class PurchaserPackageLogic extends Logic
{
    /**
     * 获取套餐列表
     * @return array
     */
    public static function getPackageList()
    {
        try {
            $packages = PurchaserPackage::where('status', 1)
                ->order('sort ASC, id ASC')
                ->select()
                ->toArray();
            
            return $packages;
        } catch (\Exception $e) {
            self::setError($e->getMessage());
            return [];
        }
    }
    
    /**
     * 创建采购套餐购买订单
     * @param array $params
     * @return array|false
     */
    public static function createOrder($params)
    {
        try {
            Db::startTrans();
            
            // 验证套餐是否存在
            $package = PurchaserPackage::where('id', $params['package_id'])
                ->where('status', 1)
                ->find();
            
            if (!$package) {
                throw new \Exception('套餐不存在或已下架');
            }
            
            // 检查商家是否存在
            $shop = Db::name('shop')->where('id', $params['shop_id'])->find();
            if (!$shop) {
                throw new \Exception('商家不存在');
            }
            
            // 检查是否有未支付的订单
            $existingOrder = PurchaserPackageOrder::where('shop_id', $params['shop_id'])
                ->where('pay_status', 0)
                ->find();
            
            if ($existingOrder) {
                throw new \Exception('您有未支付的采购套餐订单，请先完成支付或取消订单');
            }
            
            // 生成订单号
            $orderSn = 'PP' . date('YmdHis') . rand(1000, 9999);
            
            // 创建订单
            $orderData = [
                'order_sn' => $orderSn,
                'shop_id' => $params['shop_id'],
                'package_id' => $package['id'],
                'package_name' => $package['name'],
                'purchaser_count' => $package['purchaser_count'],
                'price' => $package['price'],
                'pay_status' => 0,
                'create_time' => time(),
                'update_time' => time()
            ];
            
            $orderId = PurchaserPackageOrder::insertGetId($orderData);
            
            if (!$orderId) {
                throw new \Exception('创建订单失败');
            }
            
            Db::commit();
            
            return [
                'order_id' => $orderId,
                'order_sn' => $orderSn,
                'package_name' => $package['name'],
                'purchaser_count' => $package['purchaser_count'],
                'price' => $package['price']
            ];
            
        } catch (\Exception $e) {
            Db::rollback();
            self::setError($e->getMessage());
            return false;
        }
    }
    
    /**
     * 获取订单详情
     * @param string $orderSn
     * @param int $shopId
     * @return array|false
     */
    public static function getOrderDetail($orderSn, $shopId)
    {
        try {
            $order = PurchaserPackageOrder::where('order_sn', $orderSn)
                ->where('shop_id', $shopId)
                ->find();
            
            if (!$order) {
                throw new \Exception('订单不存在');
            }
            
            return $order->toArray();
            
        } catch (\Exception $e) {
            self::setError($e->getMessage());
            return false;
        }
    }
    
    /**
     * 获取商家的采购套餐订单列表
     * @param int $shopId
     * @param array $params
     * @return array
     */
    public static function getOrderList($shopId, $params = [])
    {
        try {
            $where = [['shop_id', '=', $shopId]];
            
            // 支付状态筛选
            if (isset($params['pay_status']) && $params['pay_status'] !== '') {
                $where[] = ['pay_status', '=', $params['pay_status']];
            }
            
            $pageNo = $params['page_no'] ?? 1;
            $pageSize = $params['page_size'] ?? 10;
            
            $orders = PurchaserPackageOrder::where($where)
                ->order('create_time DESC')
                ->page($pageNo, $pageSize)
                ->select()
                ->toArray();
            
            // 格式化数据
            foreach ($orders as &$order) {
                $order['pay_status_text'] = $order['pay_status'] == 1 ? '已支付' : '未支付';
                $order['pay_time_text'] = $order['pay_time'] ? date('Y-m-d H:i:s', $order['pay_time']) : '';
                $order['create_time_text'] = date('Y-m-d H:i:s', $order['create_time']);
            }
            
            // 获取总数
            $total = PurchaserPackageOrder::where($where)->count();
            
            return [
                'lists' => $orders,
                'count' => $total,
                'page_no' => $pageNo,
                'page_size' => $pageSize
            ];
            
        } catch (\Exception $e) {
            self::setError($e->getMessage());
            return [
                'lists' => [],
                'count' => 0,
                'page_no' => 1,
                'page_size' => 10
            ];
        }
    }
    
    /**
     * 取消未支付订单
     * @param string $orderSn
     * @param int $shopId
     * @return bool
     */
    public static function cancelOrder($orderSn, $shopId)
    {
        try {
            $order = PurchaserPackageOrder::where('order_sn', $orderSn)
                ->where('shop_id', $shopId)
                ->where('pay_status', 0)
                ->find();
            
            if (!$order) {
                throw new \Exception('订单不存在或已支付，无法取消');
            }
            
            $order->delete();
            
            return true;
            
        } catch (\Exception $e) {
            self::setError($e->getMessage());
            return false;
        }
    }
    
    /**
     * 获取商家已分配的采购人员
     * @param int $shopId
     * @return array
     */
    public static function getAllocatedPurchasers($shopId)
    {
        try {
            $allocations = Db::name('purchaser_allocation')
                ->alias('pa')
                ->leftJoin('purchaser p', 'pa.purchaser_id = p.id')
                ->where('pa.shop_id', $shopId)
                ->where('pa.status', 1)
                ->where('pa.expire_time', '>', time())
                ->field('pa.*, p.name as purchaser_name, p.mobile, p.level, p.email')
                ->order('pa.allocated_time DESC')
                ->select();
            
            // 格式化数据
            foreach ($allocations as &$allocation) {
                $allocation['level_text'] = self::getPurchaserLevelText($allocation['level']);
                $allocation['allocated_time_text'] = date('Y-m-d H:i:s', $allocation['allocated_time']);
                $allocation['expire_time_text'] = date('Y-m-d H:i:s', $allocation['expire_time']);
            }
            
            return $allocations;
            
        } catch (\Exception $e) {
            self::setError($e->getMessage());
            return [];
        }
    }
    
    /**
     * 获取采购人员等级文本
     * @param int $level
     * @return string
     */
    private static function getPurchaserLevelText($level)
    {
        $levels = [
            1 => '初级采购',
            2 => '中级采购',
            3 => '高级采购'
        ];
        
        return $levels[$level] ?? '未知等级';
    }
}
