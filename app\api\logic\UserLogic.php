<?php
namespace app\api\logic;

use app\api\controller\Account;
use app\common\basics\Logic;
use app\common\enum\ClientEnum;
use app\common\logic\ChatLogic;
use app\common\logic\ChatLogic as CommonChatLogic;
use app\common\model\agent\Agent;
use app\common\model\agent\AgentBank;
use app\common\model\community\CommunityFollow;
use app\common\model\goods\GoodsCollect;
use app\common\model\kefu\ChatRecord;
use app\common\model\kefu\ChatRelation;
use app\common\model\kefu\Kefu;
use app\common\model\user\User;
use app\common\model\order\Order;
use app\common\model\AfterSale;
use app\common\model\CouponList;
use app\common\model\user\UserAuth;
use app\common\model\user\UserLevel;
use app\admin\logic\agent\AgentLogic;
use app\common\server\ConfigServer;
use app\common\model\AccountLog;
use app\common\server\JsonServer;
use app\common\server\UrlServer;
use app\common\server\WeChatServer;
use app\common\server\storage\Driver as StorageDriver;
use app\shop\validate\BankValidate;
use app\shopapi\logic\ShopLogic;
use think\facade\Db;
use EasyWeChat\Factory;
use think\facade\Cache;
use EasyWeChat\Kernel\Exceptions\Exception;

class UserLogic extends Logic
{
    public static function  center($user_id)
    {
        $user = User::findOrEmpty($user_id);
        if($user->isEmpty()) {
            return [];
        }
        // 头像
        $user->avatar = UrlServer::getFileUrl($user->avatar);
        //待支付
        $user->wait_pay = Order::where(['del'=>0,'user_id'=>$user_id,'order_status'=>Order::STATUS_WAIT_PAY,'pay_status'=>0])->count();
        //待发货
        $user->wait_delivery = Order::where(['del'=>0,'user_id'=>$user_id,'order_status'=>Order::STATUS_WAIT_DELIVERY,'pay_status'=>1])->count();
        //待收货
        $user->wait_take = Order::where(['del'=>0,'user_id'=>$user_id,'order_status'=>Order::STATUS_WAIT_RECEIVE,'pay_status'=>1])->count();
        //待评论
        $user->wait_comment = Order::alias('o')
            ->join('order_goods og','o.id = og.order_id')
            ->where(['del'=>0,'user_id'=>$user_id,'order_status'=>Order::STATUS_FINISH,'og.is_comment'=>0])
            ->count('og.id');
        //售后中
        $user->after_sale = AfterSale::where(['del'=>0,'user_id'=>$user_id])
            ->where('status','<>',AfterSale::STATUS_SUCCESS_REFUND)
            ->count();
        // 优惠券
        $user->coupon = self::ableUsedCouponCount($user_id);
        //分销开关
        $user->distribution_setting = ConfigServer::get('distribution', 'is_open',1);


        //消息数量
        $user->notice_num = 0;
        //收藏商品数量
        $user->collect = GoodsCollect::where(['user_id' => $user_id, 'status' => 1])->count();
        //商圈关注人
        $user['follow_count']=CommunityFollow::where(['user_id' => $user_id,'status'=>1])->count();
        $article=Db::name('community_article')->where(['user_id' => $user_id,'status'=>1,'del'=>0])->count();
        $exhibition=Db::name('community_exhibition')->where(['user_id' => $user_id,'status'=>1,'del'=>0])->count();
        $user['community_article_count']=$article+$exhibition;
         $user['tier_level']='';
        if($user['shop_id']){
           $user['tier_level']=Db::name('shop')->where('id',$user['shop_id'])->value('tier_level');
        }
        if($user['tier_level']===''){
            $user['go_status']='ruzhu';
        }else if($user['tier_level']===0){
              $user['go_status']='shengji';
        }else{
            $user['go_status']='bupanduan';
        }





        $user['agent_code']=Agent::where(['user_id' => $user_id])->value('agent_code');
        $user['agent_id']=Agent::where(['user_id' => $user_id])->value('id');
        // 是否设置支付密码
        $user['hasPayPassword'] = $user['pay_password'] ? 1: 0;
        $user['vip_time'] =$user['vip_time']?date('Y-m-d',$user['vip_time']):'';//集采购会员到期时间
        $user->visible(['go_status','tier_level','agent_id','community_article_count','follow_count','agent_code','is_agent','jcvip','vip_time','id','nickname','sn','avatar', 'mobile', 'hasPayPassword','next_level_tips','user_money','total_order_amount','total_recharge_amount',
            'coupon','user_integral','level','wait_pay','wait_take','wait_delivery',
            'wait_comment','after_sale','shop_id','distribution_setting', 'distribution_code', 'notice_num', 'collect','level_name','vip','is_new_user']);
        $user = $user->toArray();
        return $user;
    }

    public static function accountLog($user_id,$source,$type,$page,$size){
        $source_type = '';
        $where[] = ['user_id','=',$user_id];
        switch ($source){
            case 1:
                $source_type = AccountLog::money_change;
                break;
            case 2:
                $source_type = AccountLog::integral_change;
                break;
            case 3:
                $source_type = AccountLog::growth_change;

        }
        $where[] = ['source_type','in',$source_type];
        if($type && $type != 0){
            $where[] = ['change_type','=',$type];
        }

        $count = AccountLog::where($where)->count();
        $list = AccountLog::where($where)
            ->page($page,$size)
            ->order('id desc')
            ->field('id,change_amount,change_amount as change_amount_format,source_type,change_type,create_time,create_time as create_time_format')
            ->select()
            ->toArray();

        $more = is_more($count,$page,$size);  //是否有下一页

        $data = [
            'list'          => $list,
            'page_no'       => $page,
            'page_size'     => $size,
            'count'         => $count,
            'more'          => $more
        ];
        return $data;
    }

    public static function getUserLevelInfo($id)
    {
        $user = User::field('id,avatar,nickname,level,user_growth')->findOrEmpty($id);
        if($user->isEmpty()) {
            return ['error_msg' => '会员不存在'];
        }
        $user = $user->toArray();
        $level_list = UserLevel::where(['del'=>0])->order('growth_value', 'asc')->column('id,name,growth_value,image,background_image', 'id');

        // 用户会员等级
        $user['level_name'] = isset($level_list[$user['level']]) ? $level_list[$user['level']]['name'] : '-';

        // 用户头像
        $user['avatar'] = UrlServer::getFileUrl($user['avatar']);

        // 重置会员等级列表索引
        $level_list = array_values($level_list);
        // 获取当前用户对应等级的索引
        $index = null;
        foreach($level_list as $key => $item) {
            if($item['id'] == $user['level']) {
                $index = $key;
            }
        }

        // 遍历标识每个会员等级信息
        foreach($level_list as $key => &$item) {
            $diff_growth_percent = 0; //距离下个等级成长百分比
            if($index === false || $index < $key) {
                $item['lock_desc'] = '未解锁';
                $item['tips1'] = '当前成长值 '. $user['user_growth'];
                $item['tips2'] = '还需'.($item['growth_value'] - $user['user_growth']).'成长值';
                $item['current_level_status'] = -1;
            }else if($index > $key) {
                $item['lock_desc'] = '已解锁';
                $item['tips1'] = '当前高于该等级 ';
                $item['tips2'] = '';
                $item['current_level_status'] = 0;
            }else if($index == $key) {
                $item['current_level_status'] = 1;
                $item['lock_desc'] = '当前等级';
                $item['tips1'] = '当前成长值 '. $user['user_growth'];
                //下个等级
                $next_level = $level_list[$key+1] ?? [];
                if($next_level) {
                    $diff_growth_percent = round($user['user_growth'] / $next_level['growth_value'],2);
                    $item['tips2'] = '满'.$next_level['growth_value'].'可升级';
                } else {
                    $item['tips2'] = '';
                }
            }
            $item['diff_growth_percent'] = $diff_growth_percent;
            $item['image'] = empty($item['image']) ? '' : UrlServer::getFileUrl($item['image']);
            $item['background_image'] = empty($item['background_image']) ? '' : UrlServer::getFileUrl($item['background_image']);
        }

        $level_intro = ConfigServer::get('user_level', 'intro', '');
        $data = [
            'user' => $user,
            'level' => $level_list,
            'level_intro' => $level_intro
        ];

        return $data;
    }

    public static function getGrowthList($get)
    {
        $user_growth = User::where(['id'=>$get['user_id']])->value('user_growth');
        $where = [
            ['user_id', '=', $get['user_id']],
            ['source_type', 'in', AccountLog::growth_change]
        ];
        $lists = AccountLog::field('id,source_type,change_amount as change_amount_format,change_type,create_time as create_time_format')
            ->where($where)
            ->order('create_time', 'desc')
            ->page($get['page_no'], $get['page_size'])
            ->select()
            ->toArray();
        $count = AccountLog::field('id,source_type,change_amount as change_amount_format,change_type,create_time as create_time_format')
            ->where($where)
            ->count();

        $data = [
            'count' => $count,
            'lists' => $lists,
            'page_no' => $get['page_no'],
            'page_size' => $get['page_size'],
            'more' => is_more($count, $get['page_no'], $get['page_size']),
            'user_growth' => $user_growth
        ];
        return $data;
    }

    public static function myWallet($user_id)
    {
        $info = User::where(['id'=>$user_id])
            ->field('user_money,total_order_amount,total_recharge_amount')
            ->findOrEmpty();
        if($info->isEmpty()) {
            self::$error = '用户不存在';
            return false;
        }
        $info = $info->toArray();
        $info['open_racharge'] = ConfigServer::get('recharge','open_racharge',0);
        return $info;
    }

    //获取用户信息
    public static function getUserInfo($user_id)
    {
        $info = User::where(['id' => $user_id])
            ->field('id,sn,nickname,birthday,avatar,mobile,sex,create_time')
            ->find()
            ->toArray();
        $info['avatar'] = $info['avatar'] ? UrlServer::getFileUrl($info['avatar']) : '';
        $info['birthday']=date('Y-m-d', strtotime($info['birthday']));
        $info['oa_auth'] = UserAuth::where('user_id', $info['id'])->where('client', ClientEnum::oa)->value('id') ? 1: 0;
        $info['mnp_auth'] = UserAuth::where('user_id', $info['id'])->where('client', ClientEnum::mnp)->value('id') ? 1: 0;

        return $info;
    }

    //设置个人信息
    public static function setUserInfo($post)
    {
        try{
            $field = $post['field'];
            $value = $post['value'];

            // 确保 field 和 value 是字符串类型
            if (is_object($field)) {
                $field = (string)$field;
            }
            if (is_object($value)) {
                $value = (string)$value;
            }

            if ($field == 'avatar') {
                $value = UrlServer::setFileUrl($value);
            }
            User::where(['id'=> $post['user_id']])
                ->update([$field => $value]);
            return true;
        }catch(\Exception $e) {
            self::$error = $e->getMessage();
            return false;
        }
    }

    //更新微信信息
    public static function updateWechatInfo($user_id, $post)
    {
        Db::startTrans();
        try{
            $time = time();
            $avatar_url = is_string($post['avatar']) ? $post['avatar'] : '';
            $nickanme  = is_string($post['nickname']) ? $post['nickname'] : '';
            $sex = isset($post['sex']) ? $post['sex'] : 0;

            $config = [
                'default' => ConfigServer::get('storage', 'default', 'local'),
                'engine'  => ConfigServer::get('storage_engine')
            ];

            $avatar = ''; //头像路径
            if ($config['default'] == 'local') {
                $file_name = md5($user_id . $time. rand(10000, 99999)) . '.jpeg';
                $avatar = download_file($avatar_url, 'uploads/user/avatar/', $file_name);
            } else {
                $avatar = 'uploads/user/avatar/' . md5($user_id . $time. rand(10000, 99999)) . '.jpeg';
                $StorageDriver = new StorageDriver($config);
                if (!$StorageDriver->fetch($avatar_url, $avatar)) {
                    throw new Exception( '头像保存失败:'. $StorageDriver->getError());
                }
            }

            User::where(['id' => $user_id])->update([
                'nickname'  => $nickanme,
                'avatar' => $avatar,
                'sex' => $sex
            ]);

            Db::commit();
            return true;

        } catch(\Exception $e) {
            Db::rollback();
            self::$error = $e->getMessage();
            return false;
        }
    }

    //获取微信手机号
    public static function getMobileByMnp($post)
    {
        Db::startTrans();
        try {
            $config = WeChatServer::getMnpConfig();
            $app = Factory::miniProgram($config);
            $response = $app->auth->session($post['code']);
            if (!isset($response['session_key'])) {
                throw new Exception('获取用户信息失败');
            }
            $response = $app->encryptor->decryptData($response['session_key'], $post['iv'], $post['encrypted_data']);

            $isExist = User::where([
                ['mobile', '=', $response['phoneNumber']],
                ['id', '<>', $post['user_id']]
            ])->findOrEmpty();

            if (!$isExist->isEmpty()) {
                throw new Exception('手机号已被其他账号绑定');
            }

            User::update(['id' => $post['user_id'], 'mobile' => $response['phoneNumber']]);
            Db::commit();
            return true;
        } catch (Exception $e) {
            Db::rollback();
            self::$error = $e->getMessage();
            return false;
        }
    }

    //修改手机号
    public static function changeMobile($user_id, $data)
    {
        $user = User::find($user_id);
        $user->mobile = is_string($data['new_mobile']) ? $data['new_mobile'] : '';
        $user->save();
        return $user;
    }

    //我的粉丝列表
    public static function fans($user_id, $get, $page, $size)
    {
        $where = [];
        if (isset($get['keyword']) && $get['keyword'] != ''){
            $where[] = ['nickname|mobile','like','%'.$get['keyword'].'%'];
        }

        //查询条件
        $type = $get['type'] ?? 'all';
        switch ($type){
            case 'first':
                $where[] = ['first_leader', '=', $user_id];
                break;
            case 'second':
                $where[] = ['second_leader', '=', $user_id];
                break;
            default:
                $where[] = ['first_leader|second_leader', '=', $user_id];
        }

        $field = 'u.id, avatar, nickname, mobile, u.create_time, order_num as fans_order,
                  order_amount as fans_money, fans as fans_team';

        $count = User::alias('u')
            ->field($field)
            ->leftJoin('user_distribution d', 'd.user_id = u.id')
            ->where($where)
            ->count();

        $lists = User::alias('u')
            ->field($field)
            ->leftJoin('user_distribution d', 'd.user_id = u.id')
            ->where($where)
            ->page($page, $size)
            ->order(self::fansListsSort($get))
            ->select();

        foreach ($lists as &$item) {
            $item['avatar'] = UrlServer::getFileUrl($item['avatar']);
            $item['fans_team'] = $item['fans_team'] ?? 0;
            $item['fans_order'] = $item['fans_order'] ?? 0;
            $item['fans_money'] = $item['fans_money'] ?? 0;
            unset($item['fans'], $item['distribution_order_num'], $item['distribution_money']);
        }

        $data = [
            'list' => $lists,
            'page' => $page,
            'size' => $size,
            'count' => $count,
            'more' => is_more($count, $page, $size)
        ];
        return $data;
    }

    //粉丝列表排序
    public static function fansListsSort($get)
    {
        if (isset($get['fans']) && $get['fans'] != ''){
            return ['fans_team' =>  $get['fans'], 'u.id' => 'desc'];
        }

        if (isset($get['money']) && $get['money'] != ''){
            return ['fans_money' =>  $get['money'], 'u.id' => 'desc'];
        }

        if (isset($get['order']) && $get['order'] != ''){
            return ['fans_order' =>  $get['order'], 'u.id' => 'desc'];
        }

        return ['u.id' =>  'desc'];
    }


    /**
     * @notes 获取聊天记录
     * @param $user_id 当前用户ID
     * @param $to_id 聊天对象用户ID（用户对用户聊天时使用）
     * @param $shop_id 商家ID（客服聊天时使用）
     * @param $page 页码
     * @param $size 每页数量
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date 2021/12/29 18:20
     */
    public static function getChatRecord($user_id,$to_id,$shop_id, $page, $size)
    {
        self::clearUnreadBadgeCache($user_id);
        // 如果shop_id不为空，获取与商家客服的聊天记录
        if (!empty($shop_id)) {
            return self::getKefuChatRecord($user_id, $shop_id, $page, $size);
        }

        // 如果shop_id为空且to_id不为空，获取用户对用户的聊天记录
        if (!empty($to_id)) {
            return self::getUserToChatRecord($user_id, $to_id, $page, $size);
        }
       
      
       
        // 如果两个参数都为空，返回空结果
        return [
            'config' => 0,
            'kefu' => null,
            'record' => [
                'list' => [],
                'page' => $page,
                'size' => $size,
                'count' => 0,
                'more' => false
            ]
        ];
    }
/**
     * 清除用户未读消息缓存（在消息状态变更时调用）
     * @param int $user_id 用户ID
     * @return void
     */
    public static function clearUnreadBadgeCache($user_id)
    {
        $cacheKey = 'user_unread_badge_' . $user_id;
        Cache::delete($cacheKey);
    }
    /**
     * @notes 获取与商家客服的聊天记录
     * @param $user_id
     * @param $shop_id
     * @param $page
     * @param $size
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    private static function getKefuChatRecord($user_id, $shop_id, $page, $size)
    {
        $map1 = [
            ['shop_id', '=', $shop_id],
            ['from_id', '=', $user_id],
            ['from_type', '=', 'user'],
        ];
        $map2 = [
            ['shop_id', '=', $shop_id],
            ['to_id', '=', $user_id],
            ['to_type', '=', 'user'],
        ];
        // 通信记录
        $records = ChatRecord::whereOr([$map1, $map2])
            ->order('id desc')
            ->page($page, $size)
            ->select()->toArray();
        foreach ($records as &$item) {
            ChatRecord::update(['is_read' => 1], ['id' => $item['id'],'to_type'=>'user']);
        }

        $count = ChatRecord::whereOr([$map1, $map2])->count();

        // 上一个客服关系
        $kefu = self::getLastRelation((int)$user_id, (int)$shop_id);

        // 当前在线的所有客服
        $online = ChatLogic::getOnlineKefu($shop_id);

        // 后台配置=>[1=>人工客服; 2=>在线客服]
        if ($shop_id > 0) {
            $config = ConfigServer::get('shop_customer_service', 'type', 1, $shop_id);
        } else {
            $config = ConfigServer::get('customer_service', 'type', 1);
        }

        $kefu_id = $kefu['kefu_id'] ?? 0;
        if(empty($kefu_id)){
            $kefu_id= Kefu::where(['shop_id' => $shop_id,'disable'=>0])->value('id');
        }
        $records = ChatLogic::formatChatRecords($records, $count, $page, $size);

        // 没有在线客服或者后台配置为 人工客服
        if (empty($online) || $config == 1) {
            $kefu = Kefu::where(['id' => $kefu_id])
                ->field(['id', 'nickname', 'avatar'])
                ->findOrEmpty();
            $kefu['shop_name']=$kefu['nickname']=Db::name('shop')->where('id',$shop_id)->value('name');
            return ['config' => $config, 'kefu' => $kefu, 'record' => $records];
        }

        // 没有聊天记录(未与客服聊天) 或者 曾经的聊天客服不在线
        if (empty($kefu) || !in_array($kefu_id, $online)) {
            // 随机分配客服
            $rand = rand(0, count($online) - 1);
            $kefu_id = $online[$rand];
        }

        $kefu = Kefu::where(['id' => $kefu_id])
            ->field(['id', 'nickname', 'avatar'])
            ->findOrEmpty();
        $kefu['shop_name']=$kefu['nickname']=Db::name('shop')->where('id',$shop_id)->value('name');
        return ['config' => $config, 'kefu' => $kefu, 'record' => $records];
    }

    /**
     * @notes 获取用户对用户的聊天记录
     * @param $user_id 当前用户ID
     * @param $to_id 聊天对象用户ID
     * @param $page 页码
     * @param $size 每页数量
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    private static function getUserToChatRecord($user_id, $to_id, $page, $size)
    {
        // 用户对用户聊天记录查询条件（shop_id为0表示用户间聊天）
        $map1 = [
            ['shop_id', '=', 0],
            ['from_id', '=', $user_id],
            ['from_type', '=', 'user'],
            ['to_id', '=', $to_id],
            ['to_type', '=', 'user'],
        ];
        $map2 = [
            ['shop_id', '=', 0],
            ['from_id', '=', $to_id],
            ['from_type', '=', 'user'],
            ['to_id', '=', $user_id],
            ['to_type', '=', 'user'],
        ];

        // 获取聊天记录
        $records = ChatRecord::whereOr([$map1, $map2])
            ->order('id desc')
            ->page($page, $size)
            ->select()->toArray();

        // 标记消息为已读（只标记发给当前用户的消息）
        foreach ($records as &$item) {
            if ($item['to_id'] == $user_id) {
                ChatRecord::update(['is_read' => 1], ['id' => $item['id']]);
            }
        }

        $count = ChatRecord::whereOr([$map1, $map2])->count();

        // 格式化聊天记录
        $records = ChatLogic::formatChatRecords($records, $count, $page, $size);

        // 获取聊天对象用户信息
        $chatUser = User::where('id', $to_id)
            ->field('id,nickname,avatar')
            ->findOrEmpty();

        // 返回用户对用户聊天的特殊格式
        return [
            'config' => 1, // 默认允许聊天
            'kefu' => null, // 用户聊天没有客服
            'chat_user' => [
                'id' => $chatUser['id'] ?? 0,
                'nickname' => $chatUser['nickname'] ?? '',
                'avatar' => $chatUser['avatar'] ? UrlServer::getFileUrl($chatUser['avatar']) : ''
            ],
            'chat_type' => 'user_chat', // 标识为用户聊天
            'record' => $records
        ];
    }


    public static function getChatUserList($user_id,$get, $page, $size)
    {
        $where[] = ['user_id', '=', $user_id];
        // if (isset($get['nickname']) && $get['nickname']) {
        //     $where[] = ['nickname', 'like', '%' . $get['nickname'] . '%'];
        // }

        $online_user = CommonChatLogic::getOnlineUser();
        // 确保 $online_user 是数组
        if (empty($online_user) || !is_array($online_user)) {
            $online_user = [];
        }

        $exp = 'update_time desc';
        if (!empty($online_user)) {
            $user_id_str = implode(",", $online_user);
            $exp = "field(user_id," . $user_id_str . ") desc, update_time desc";
        }

        // 获取所有聊天关系记录（包括客服聊天和用户聊天）
        $lists = ChatRelation::where($where)
            ->page($page, $size)
            ->orderRaw($exp)
            ->select();
        $no_read = 0;
        $count = ChatRelation::where($where)->count();
        foreach ($lists as &$item) {
            $item['online'] = 0;
            $actual_contact_id = null; // 用于存储实际的联系人ID

            // 根据shop_id判断聊天类型
            if ($item['shop_id'] == 0) {
                // 用户对用户聊天处理
                $item['chat_type'] = 'user';
                $contact_user_id = $item['kefu_id']; // kefu_id字段存储的是联系人用户ID

                // 检查kefu_id是否真的是用户ID，如果不是，尝试从聊天记录中获取真实的联系人
                $contact_user = Db::name('user')->where(['id' => $contact_user_id])->find();
                if (empty($contact_user)) {
                    // 如果kefu_id对应的用户不存在，从聊天记录中查找真实的联系人
                    $chat_record = Db::name('chat_record')
                        ->where([
                            ['shop_id', '=', 0],
                            ['from_id|to_id', '=', $item['user_id']]
                        ])
                        ->where(function($query) use ($item) {
                            $query->where([
                                ['from_id', '<>', $item['user_id']],
                                ['from_type', '=', 'user']
                            ])->whereOr([
                                ['to_id', '<>', $item['user_id']],
                                ['to_type', '=', 'user']
                            ]);
                        })
                        ->order('id desc')
                        ->find();

                    if ($chat_record) {
                        $contact_user_id = ($chat_record['from_id'] == $item['user_id']) ? $chat_record['to_id'] : $chat_record['from_id'];
                        $contact_user = Db::name('user')->where(['id' => $contact_user_id])->find();
                    }
                }

                $actual_contact_id = $contact_user_id; // 保存实际的联系人ID

                if (in_array($contact_user_id, $online_user)) {
                    $item['online'] = 1;
                }

                $item['contact_user'] = $contact_user;
                $item['nickname'] = $contact_user['nickname'] ?? '';
                $item['avatar'] = '';
                if (!empty($contact_user['avatar'])) {
                    $item['avatar'] = UrlServer::getFileUrl($contact_user['avatar']);
                }
                $item['shop_name'] = ''; // 用户聊天没有商家
            } else {
                // 客服聊天处理（原有逻辑）
                $item['chat_type'] = 'kefu';
                if (in_array($item['user_id'], $online_user)) {
                    $item['online'] = 1;
                }
                $item['shop_name']=Db::name('shop')->where(['id' => $item['shop_id']])->value('name');
                $item['kefu']=Db::name('kefu')->where(['id' => $item['kefu_id']])->find();
                $item['nickname']= $item['shop_name'];
                // 修复空值访问问题
                $item['avatar'] = '';
                if (!empty($item['kefu']) && !empty($item['kefu']['avatar'])) {
                    $item['avatar'] = UrlServer::getFileUrl($item['kefu']['avatar']);
                }
            }
            //获取最后一条聊天记录
            if ($item['chat_type'] === 'user') {
                // 用户对用户聊天记录查询，使用实际的联系人用户ID
                $query_contact_id = $actual_contact_id ?? $item['kefu_id'];
                $msg = ChatRecord::where([
                    ['shop_id', '=', 0],
                    ['from_id|to_id', '=', $query_contact_id], // 使用实际的联系人用户ID
                    ['from_id|to_id', '=', $item['user_id']]
                ])->order('id desc')->find();
            } else {
                // 客服聊天记录查询（原有逻辑）
                $msg = ChatRecord::where([
                    ['shop_id', '=', $item['shop_id']],
                    ['from_id|to_id', '=', $item['kefu_id']],
                    ['from_id|to_id', '=', $item['user_id']]
                ])->order('id desc')->find();
            }
            if ($msg) {
                // 根据消息类型处理显示内容
                if ($msg['msg_type'] == \app\common\enum\ChatMsgEnum::TYPE_IMG) {
                    // 如果是图片类型，显示[图片]
                    $item['msg'] = '[图片]';
                } else if ($msg['msg_type'] == \app\common\enum\ChatMsgEnum::TYPE_GOODS) {
                    // 如果是商品类型，显示[商品]
                    $item['msg'] = '[商品]';
                } else if ($msg['msg_type'] == \app\common\enum\ChatMsgEnum::TYPE_VOICE) {
                    // 如果是语音类型，显示[语音]
                    $item['msg'] = '[语音]';
                } else if ($msg['msg_type'] == \app\common\enum\ChatMsgEnum::TYPE_VIDEO) {
                    // 如果是视频类型，显示[视频]
                    $item['msg'] = '[视频]';
                } else if ($msg['msg_type'] == \app\common\enum\ChatMsgEnum::TYPE_ORDER) {
                    // 如果是订单类型，显示[订单]
                    $item['msg'] = '[订单]';
                } else {
                    // 其他类型（文本）截取前10个字符
                    $item['msg'] = mb_substr($msg['msg'], 0, 10);
                }
                // 根据聊天类型判断已读状态
                if ($item['chat_type'] === 'user') {
                    // 用户聊天：如果最后一条消息是发给当前用户的，显示已读状态
                    $item['is_read'] = ($msg['to_id'] == $user_id) ? $msg['is_read'] : 1;
                } else {
                    // 客服聊天：原有逻辑
                    $item['is_read'] = $msg['to_type']=='user' ? $msg['is_read'] : 1;
                }
                $item['time_txt'] = friend_date(strtotime($msg['update_time']));
            } else {
                $item['time_txt'] = '';
                $item['update_time'] = '';
            }
           // 根据聊天类型统计未读消息
           if ($item['chat_type'] === 'user') {
               // 用户对用户聊天的未读消息，使用实际的联系人用户ID
               $query_contact_id = $actual_contact_id ?? $item['kefu_id'];
               $unread_msg = ChatRecord::where([
                   ['shop_id', '=', 0],
                   ['from_type', '=', 'user'],
                   ['to_type', '=', 'user'],
                   ['from_id', '=', $query_contact_id], // 使用实际的联系人用户ID
                   ['is_read', '=', 0],
                   ['to_id', '=', $user_id]
               ])->count();
           } else {
               // 客服聊天的未读消息（原有逻辑）
               $unread_msg = ChatRecord::where([
                   ['shop_id', '=', $item['shop_id']],
                   ['from_type', '=', 'kefu'],
                   ['to_type', '=', 'user'],
                   ['from_id', '=', $item['kefu_id']],
                   ['is_read', '=', 0],
                   ['to_id', '=', $user_id]
               ])->count();
           }

           $no_read+=$unread_msg;
        }

        //获取未读消息数量


        return [
            'list' => $lists->toArray(),
            'page' => $page,
            'size' => $size,
            'no_read' => $no_read,
            'count' => $count,
            'more' => is_more($count, $page, $size)
        ];
    }

    /**
     * @notes 获取用户对用户聊天列表
     * @param $user_id 当前用户ID
     * @param $get 请求参数
     * @param $page 页码
     * @param $size 每页数量
     * @return array
     * <AUTHOR>
     * @date 2024/12/19 15:30
     */
    public static function getUserChatList($user_id, $get, $page, $size)
    {
        // 强制设置为用户聊天类型
        $get['chat_type'] = 'user';
        return self::getChatUserList($user_id, $get, $page, $size);
    }

    /**
     * @notes 删除联系人
     * @param int $user_id 用户ID
     * @param int $kefu_id 客服ID
     * @param int $shop_id 商家ID
     * @return bool
     * <AUTHOR>
     * @date 2024/12/19 11:29
     */
    public static function deleteChatUser($user_id, $kefu_id, $shop_id)
    {
        try {
            // 删除ChatRelation表中的联系关系，但保留ChatRecord表中的聊天记录
            $result = ChatRelation::where([
                'user_id' => $user_id,
                'kefu_id' => $kefu_id,
                'shop_id' => $shop_id
            ])->delete();

            return $result > 0;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * @notes 获取用户最后通信的客服id
     * @param int $user_id
     * @param int $shop_id
     * @return array|\think\Model
     * <AUTHOR>
     * @date 2021/12/29 18:20
     */
    public static function getLastRelation(int $user_id, int $shop_id)
    {
        $relation = ChatRelation::where([
            'user_id' => $user_id,
            'shop_id' => $shop_id
        ])->order('update_time desc')->findOrEmpty();

        return $relation;
    }


    /**
     * @notes 可用优惠券数量
     * @param $get
     * @return int
     * <AUTHOR>
     * @date 2023/2/20 15:10
     */
    public static function ableUsedCouponCount($user_id)
    {
        // 提取用户未删除的优惠券
        $where = [
            ['cl.del', '=', 0],
            ['cl.user_id', '=', $user_id]
        ];
        $field = 'cl.*, c.name,c.use_time_type,c.use_time_start,c.use_time_end,c.use_time as coupon_use_time,
        c.condition_type,c.condition_money,c.money,c.use_goods_type';
        $count_list = CouponList::alias('cl')
            ->leftJoin('coupon c', 'c.id=cl.coupon_id')
            ->field($field)
            ->where($where)
            ->order('id', 'desc')
            ->select()
            ->toArray();

        // 循环标识每条记录的券是否过期
        foreach($count_list as &$item) {
            $item['is_expired'] = 0; // 默认先标识为未过期
            switch($item['use_time_type']) {
                case 1: // 固定时间
                    if($item['use_time_end'] <= time()) {
                        $item['is_expired'] = 1;
                    }
                    break;
                case 2:  // 领券当天起
                    $days = '+'.$item['coupon_use_time'].' day';
                    $expired_time = strtotime($days, strtotime($item['create_time']));
                    if($expired_time <= time()) {
                        $item['is_expired'] = 1;
                    }
                    break;
                case 3: // 领券次日起
                    $days = '+'.($item['coupon_use_time']+1).' day';
                    $expired_time = strtotime($days, strtotime($item['create_time']));
                    if($expired_time <= time()) {
                        $item['is_expired'] = 1;
                    }
                    break;
            }
        }

        $valid_array = array_filter($count_list, function($item) {
            return $item['status'] == 0 && $item['is_expired'] == 0; // 未使用，未过期
        });

        return count($valid_array);
    }



    public static function myuser($get)
    {
//        $agent_list=AgentLogic::getAllSubordinatesAgentsAndNonAgentDescendants($get['user_id']);
        $agent_list= Db::name('agent_relationships')->where(['sponsor_id'=>$get['user_id']])->column('user_id');
        if(isset($agent_list) && !empty($agent_list)){
            // 代理发展用户
            $fans = User::where([
                ['id', 'in', $agent_list],
                ['del', '=', 0]
                ])->field('id,avatar,nickname,shop_id,jcvip,agent_id')
                ->page($get['page_no'], $get['page_size'])
                ->select();

            $user_count = User::where([
                ['id', 'in', $agent_list],
                ['del', '=', 0]
            ])->count();

            if($fans){
                foreach($fans as &$val){
                    $val['avatar'] = UrlServer::getFileUrl($val['avatar']);
                }
            }
        }
        $data = [
            'count' =>$user_count??0,
            'lists' => $fans??[],
            'page_no' => $get['page_no'],
            'page_size' => $get['page_size']
        ];
        return $data;
    }


    //我的佣金明细
    public static function mymoney($get){
        try {
            $where = [];

            // 添加其他查询条件（如果有的话）
            if (!empty($get['start_time'])) {
                $where[] = ['a.create_time', '>=', strtotime($get['start_time'])];
            }

            if (!empty($get['end_time'])) {
                $where[] = ['a.create_time', '<=', strtotime($get['end_time'])];
            }
            $where[] = ['a.user_id', '=',$get['user_id']];
            // 计算每种 order_type 的待返佣和已结算费用之和
            //`money` decimal(10,2) unsigned NOT NULL COMMENT '佣金',
            //`status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1-待返佣；2-已结算；3-失效；',
            //计算每个用户的待返佣的总金额,已结算的总金额,失效的总金额
            $result['list'] = Db::name('agent_order')->alias('a')
                ->field('a.*,u.nickname,u.mobile,u.avatar,sum(case when a.status=1 then money else 0 end) as pending_commissions,sum(case when a.status=2 then money else 0 end) as settled_commissions,sum(case when a.status=3 then money else 0 end) as invalid_commissions')
                ->leftJoin('user u','u.id=a.user_id')
                ->where($where)
                ->group('a.user_id')
                ->select()
                ->toArray();
//            $order_type=[1=>'用户集采购会员',2=>'商家入驻费',3=>'商家检验费',4=>'入驻及检验费组合'];
            foreach ($result['list'] as &$item) {
                $item['avatar'] = empty($item['avatar']) ? '' : UrlServer::getFileUrl($item['avatar']);
            }
            //已结算金额
            $cn_price=Db::name('agent_order')->alias('a')
                ->leftJoin('agent_settlement u','u.agent_order_id=a.id')
                ->where($where)
                ->sum('u.cn_price');
            //已提现金额

            $apply_amount=Db::name('agent_withdrawal')
                ->where(['user_id'=>$get['user_id'],'status'=>['<',3]])
                ->sum('apply_amount');
            //提现手续费
            $fee=ConfigServer::get('agent', 'withdrawal_fee', '');;
            $ktx_explain=ConfigServer::get('agent', 'ktx_explain', '');;
            $djs_explain=ConfigServer::get('agent', 'djs_explain', '');;
            $ydj_explain=ConfigServer::get('agent', 'ydj_explain', '');;
            $ktx=$cn_price-$apply_amount;
            return [
                'pending_commissions' => $result['list'][0]['pending_commissions']??0,
                'invalid_commissions' => $result['list'][0]['invalid_commissions']??0,
                'apply_amount' =>$apply_amount,//已提现(提现中)
                'cn_price' =>$cn_price,//已结算
                'can_apply'=>$ktx?:0,//可提现
                'fee'=>$fee,//提现手续费
                'ktx_explain'=>$ktx_explain,//可提现佣金
                'djs_explain'=>$djs_explain,//待结算佣金
                'ydj_explain'=>$ydj_explain,//已冻结佣金
            ];
        } catch (\Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }

    //我的佣金明细
    public static function mymoneylist($get){
        try {
            $get['agent_id']=$get['user_id'];
            $agentLogic=new AgentLogic();
            return $agentLogic->record($get);
        } catch (\Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }

    //我的佣金支出明细
    public static function mymoneyapplylist($get){
        try {
            $get['agent_id']=$get['user_id'];
            $agentLogic=new AgentLogic();
            return $agentLogic->cashList($get);
        } catch (\Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }

    //我的佣金提现
    public static function applyWithdraw($post){
        try {
            $agentLogic=new AgentLogic();
            return $agentLogic->cashApply($post);
        } catch (\Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }



    //添加银行卡
    public static function addBank($post){
        // 确保数据类型正确
        $agent_id = isset($post['agent_id']) ? (string)$post['agent_id'] : '';
        $account = isset($post['account']) ? (string)$post['account'] : '';
        $name = isset($post['name']) ? (string)$post['name'] : '';
        $branch = isset($post['branch']) ? (string)$post['branch'] : '';
        $nickname = isset($post['nickname']) ? (string)$post['nickname'] : '';
        $mobile = isset($post['mobile']) ? (string)$post['mobile'] : '';

        //判断银行卡账号是否存在
        $bank_info=Db::name('agent_bank')->where(['agent_id'=>$agent_id,'account'=>$account,'del'=>0])->find();
        if(!empty($bank_info)){
            return false;
        }

        $shop_bank = new AgentBank();
        $shop_bank->agent_id     = $agent_id;
        $shop_bank->name        = $name;
        $shop_bank->branch      = $branch;
        $shop_bank->nickname    = $nickname;
        $shop_bank->mobile    = $mobile;
        $shop_bank->account     = $account;
        $shop_bank->save();
        return true;
    }

    //添加银行卡
    public static function getBankCard($get){

        $shop_bank = AgentBank::where('agent_id',$get['agent_id'])
            ->field('id,name,branch,nickname,account,mobile')
            ->where(['del'=>0])
            ->select()->toArray();
        foreach ($shop_bank as &$val){
            $bank_logo=Db::name('bank')->where(['bank_name'=>$val['name']])->value('bank_logo');
            $val['bank_logo'] = UrlServer::getFileUrl($bank_logo);
        }
        return $shop_bank;
    }


    //添加支付宝
    public static function addAlipay($post){
        // 确保数据类型正确
        $agent_id = isset($post['agent_id']) ? (string)$post['agent_id'] : '';
        $account = isset($post['account']) ? (string)$post['account'] : '';
        $username = isset($post['username']) ? (string)$post['username'] : '';

        //判断支付宝账号是否存在
        $bank_info=Db::name('agent_alipay')->where(['agent_id'=>$agent_id,'account'=>$account])->find();
        if(!empty($bank_info)){
            return false;
        }

        $data['agent_id']     = $agent_id;
        $data['account']     = $account;
        $data['username']     = $username;
        Db::name('agent_alipay')->insert($data);
        return true;
    }


    //添加银行卡
    public static function getAlipayInfo($post){



        return Db::name('agent_alipay')->where('agent_id',$post['agent_id'])->find();

    }

    //删除银行卡
    public static function delBank($post){
        //判断银行卡是否存在
        $bank_info=Db::name('agent_bank')->where(['id'=>$post,'del'=>0])->find();
        if(empty($bank_info)){
            return false;
        }


        Db::name('agent_bank')->where('id',$post)->update(['del'=>1]);
        return true;
    }


    //添加银行卡
    public static function bankList(){
        //存缓存
        $cache_key = 'bankList';
        if(Cache::has($cache_key)){
            return Cache::get($cache_key);
        }
        else{
            $data['list']=Db::name('bank')->select()->toArray();
            $data['count']=Db::name('bank')->count();
            foreach ($data['list'] as &$val){
                $val['bank_logo'] = UrlServer::getFileUrl($val['bank_logo']);
            }
            Cache::set($cache_key,$data);
        }
        return $data;
    }

    /**
     * @notes 申请代理保证金退款
     * @param $user_id
     * @return bool|string|array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function applyAgentDepositRefund($user_id,$post)
    {
        if(!isset($post['document_path'])){
             return [
                    'code' => 0,
                    'show' => 1,
                    'msg' => '请上传签字后的退款协议',
                    'data' => [
                        'can_deactivate' => false
                    ]
                ];
        }



        // 先检查条件
        $checkResult = self::checkAgentRefundCondition($user_id);
        if (!$checkResult['data']['can_deactivate']) {
            // 返回完整的检查结果，让前端可以显示详细信息
            return $checkResult;
        }

        $depositOrder = Db::name('agent_merchantfees')
            ->where('user_id', $user_id)
            ->where('status', 'in', [1, 2]) // 1已支付(公示期), 2公示期结束(可退)
            ->order('id', 'desc') // 获取最新的有效保证金订单
            ->find();
        if (!$depositOrder) {
            return [
                'code' => 0,
                'show' => 1,
                'msg' => '未找到有效的保证金支付记录',
                'data' => [
                    'can_deactivate' => false
                ]
            ];
        }

        if ($depositOrder['status'] == 1) { // 状态为1：公示期
            if (time() < $depositOrder['publicity_period_end_time']) {
                $endDate = date('Y-m-d H:i:s', $depositOrder['publicity_period_end_time']);
                return [
                    'code' => 0,
                    'show' => 1,
                    'msg' => '保证金仍在公示期，请于 ' . $endDate . ' 后再申请退款',
                    'data' => [
                        'can_deactivate' => false
                    ]
                ];
            }
            // 公示期已过，但状态未更新，自动更新为可退状态
            Db::name('agent_merchantfees')->where('id', $depositOrder['id'])->update(['status' => 2]);
            $depositOrder['status'] = 2;
        }

        // 获取配置的申请后公示期天数
        $configData = ConfigServer::get('agent_setting', '');
        $config = [];

        // 如果配置存在，检查它的类型
        if (!empty($configData)) {
            // 如果是字符串，尝试解析JSON
            if (is_string($configData)) {
                $parsedData = json_decode($configData, true);
                if (is_array($parsedData)) {
                    $config = $parsedData;
                }
            }
            // 如果已经是数组，直接使用
            else if (is_array($configData)) {
                $config = $configData;
            }
        }

        // 如果没有获取到有效配置，使用默认值
        if (!isset($config['refund_publicity_period_days'])) {
            $config['refund_publicity_period_days'] = 90;
        }

        // 计算申请后公示期结束时间
        $refundPublicityEndTime = time() + ($config['refund_publicity_period_days'] * 86400); // 86400秒=1天

        // 更新状态为退款申请中，并记录申请时间和公示期结束时间
        $updateResult = Db::name('agent_merchantfees')
            ->where('id', $depositOrder['id'])
            ->where('status', 2) // 再次确认状态，防止并发问题
            ->update([
                'status' => 3,
                'refund_request_time' => time(),
                'document_path'=>$post['document_path'],
                'refund_publicity_end_time' => $refundPublicityEndTime
            ]);

        if ($updateResult) {
            // 可选：发送通知给管理员
            // event('AdminNotice', ['scene' => 'agent_deposit_refund_apply', 'params' => ['user_id' => $user_id, 'order_id' => $depositOrder['id']]]);
            return [
                'code' => 1,
                'show' => 0,
                'msg' => '申请退款成功',
                'data' => [
                    'can_deactivate' => true,
                    'deposit_id' => $depositOrder['id'],
                    'refund_publicity_end_time' => date('Y-m-d H:i:s', $refundPublicityEndTime),
                    'refund_publicity_days' => $config['refund_publicity_period_days']
                ]
            ];
        } else {
            return [
                'code' => 0,
                'show' => 1,
                'msg' => '申请退款失败，请稍后再试',
                'data' => [
                    'can_deactivate' => false
                ]
            ];
        }
    }

    /**
     * 获取不同类型的用户列表
     * @param int $user_id 当前用户ID
     * @param int $type 用户类型：
     *                  0-我的用户(自己发展的)
     *                  1-我的会员(购买了集采购会员的用户)
     *                  2-我的商家(购买了商家入驻费的用户)
     *                  3-我的代理(发展的用户成为代理)
     * @param int $page 页码
     * @param int $size 每页数量
     * @return array 用户列表数据
     */
    public static function getUsersByType($user_id, $type, $page, $size)
    {
        $where = [];
        $data = [
            'lists' => [],
            'page' => $page,
            'size' => $size,
            'count' => 0,
            'more' => false
        ];
     $my_users = Db::name('agent_relationships')
                        ->where('sponsor_id', $user_id)
                        ->column('user_id');
        try {
            switch ($type) {
                case 0: // 我的用户(自己发展的)
                    // 查询agent_relationships表中sponsor_id为自己的用户
                    $user_ids = Db::name('agent_relationships')
                        ->where('sponsor_id', $user_id)
                        ->column('user_id');

                    if (empty($user_ids)) {
                        return $data;
                    }

                    $where[] = ['id', 'in', $user_ids];
                    break;

                case 1: // 我的会员(购买了集采购会员的用户)
                    // 查询agent_order表中user_id为自己且order_type为1的所有g_user_id
                    $user_ids = Db::name('agent_order')
                        ->where([
                            ['user_id', '=', $user_id],
                            ['order_type', '=', 1]
                        ])
                        ->column('g_user_id');

                    if (empty($user_ids)) {
                        return $data;
                    }

                    $where[] = ['id', 'in', $user_ids];
                    break;

                case 2: // 我的商家(购买了商家入驻费的用户)
                    // 查询agent_order表中user_id为自己且order_type为2,3,4的所有g_user_id
                    if(!empty($my_users)){
                        $user_ids = Db::name('user')
                            ->where([
                                ['shop_id', '>', 0],
                                ['id', 'in', $my_users]
                            ])
                            ->column('id');
                        }
                    if (empty($user_ids)) {
                        return $data;
                    }

                    $where[] = ['id', 'in', $user_ids];
                    break;

                case 3: // 我的代理(发展的用户成为代理)
                    // 查询agent_relationships表中sponsor_id为自己的所有user_id
                    $user_ids = Db::name('agent_relationships')
                        ->where('sponsor_id', $user_id)
                        ->column('user_id');

                    if (empty($user_ids)) {
                        return $data;
                    }

                    // 查询这些用户中是代理且代理未被删除的用户
                    $valid_agent_user_ids = Db::name('agent')
                        ->where('user_id', 'in', $user_ids)
                        ->where('del', 0) // 只查询未删除的代理
                        ->column('user_id');

                    if (empty($valid_agent_user_ids)) {
                        return $data;
                    }

                    // 再查询这些user_id中是代理的用户
                    $where[] = ['id', 'in', $valid_agent_user_ids];
                    $where[] = ['is_agent', '=', 1];
                    break;

                default:
                    return $data;
            }

            // 查询符合条件的用户总数
            $count = User::where($where)->count();

            // 查询用户列表
            $list = User::where($where)
                ->field('id, nickname, avatar, mobile')
                ->page($page, $size)
                ->select()
                ->toArray();

            // 处理头像URL
            foreach ($list as &$item) {
                $item['avatar'] = UrlServer::getFileUrl($item['avatar']);
            }

            $data = [
                'lists' => $list,
                'page' => $page,
                'size' => $size,
                'count' => $count,
                'more' => is_more($count, $page, $size)
            ];

            return $data;
        } catch (\Exception $e) {
            self::$error = $e->getMessage();
            return $data;
        }
    }
    /*
     * 我的保证金(代理)
     * @param array $get 请求参数
     * @return array 保证金信息和明细
     */
     static public function myAgentDeposit($get)
    {
        $user_id = $get['user_id'];
        $page = $get['page_no'] ?? 1;
        $size = $get['page_size'] ?? 15;

        // 检查用户是否是代理
        $agent = Db::name('agent')
            ->where('user_id', $user_id)
            ->where('del', 0) // 只检查未删除的代理
            ->find();

        if (!$agent) {
            return [
                'lists' => [],
                'page' => $page,
                'size' => $size,
                'count' => 0,
                'more' => 0,
                'message' => '您不是代理或代理已被删除'
            ];
        }

        try {
            // 获取代理保证金信息
            $deposit = Db::name('agent_merchantfees')
                ->where('user_id', $user_id)
                ->where('status', 'in', [1, 2, 3]) // 已支付的保证金记录
                ->order('id', 'desc')
                ->find();

            // 如果没有保证金记录，返回空数据
            if (!$deposit) {
                return [
                    'deposit' => null,
                    'details' => [
                        'count' => 0,
                        'lists' => [],
                        'page' => $page,
                        'size' => $size,
                        'more' => false
                    ]
                ];
            }

            // 获取保证金明细
            $where = [
                ['user_id', '=', $user_id],
                ['deposit_id', '=', $deposit['id']]
            ];

            // 如果有筛选条件
            if (isset($get['change_type']) && $get['change_type'] !== '') {
                $where[] = ['change_type', '=', $get['change_type']];
            }

            // 查询明细记录
            $details = Db::name('agent_deposit_details')
                ->where($where)
                ->order('id', 'desc')
                ->page($page, $size)
                ->select()
                ->toArray();

            // 获取明细总数
            $count = Db::name('agent_deposit_details')
                ->where($where)
                ->count();
        $statusMap = [
            0 => '未支付',
            1 => '已缴纳',
            2 => '公示期结束(可退)',
            3 => '退款申请中',
            4 => '已退款',
            5 => '退款失败'
        ];
            // 处理明细数据
            foreach ($details as &$item) {
                // 格式化日期
                $item['created_at_format'] = date('Y-m-d H:i:s', strtotime($item['created_at']));
                // 变动类型文本
                $types = [
                    1 => '缴纳',
                    2 => '增加',
                    3 => '扣除',
                    4 => '退还'
                ];
                $item['change_type_text'] = $types[$item['change_type']] ?? '未知';
            }

            // 计算当前保证金余额
            $changes_sum = Db::name('agent_deposit_details')
                ->where('deposit_id', $deposit['id'])
                ->sum('deposit_change');

            $current_balance = $deposit['amount'] + floatval($changes_sum);
            $current_balance = max(0, $current_balance); // 确保余额不为负数

            // 格式化保证金信息
            $deposit['current_balance'] = number_format($current_balance, 2);
            $deposit['payment_date_format'] = date('Y-m-d H:i:s', $deposit['payment_date']);
            $deposit['status_text'] =$statusMap[$deposit['status']];
            // 返回结果
            return [
//                'deposit' => $deposit,

                    'count' => $count,
                    'lists' => $details,
                    'page' => $page,
                    'size' => $size,
                    'more' => is_more($count, $page, $size)

            ];
        } catch (\Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }

    /**
     * 获取保证金状态文本
     * @param int $status 状态码
     * @return string 状态文本
     */
    private static function getDepositStatusText($status)
    {
        $statusMap = [
            0 => '未支付',
            1 => '已支付',
            2 => '公示期结束(可退)',
            3 => '退款申请中',
            4 => '已退款',
            5 => '退款失败'
        ];
        return $statusMap[$status] ?? '未知状态';
    }

    /**
     * @notes 获取代理退款须知
     * @return array
     */
    public static function getAgentRefundNotice()
    {
        // 从配置表中获取代理退款须知的富文本内容
        $notice = ConfigServer::get('agent', 'agent_deposit_refund_warning_image','');

        // 获取代理保证金公示期天数
        $configData = ConfigServer::get('agent_setting', '');
        $config = [];

        // 如果配置存在，检查它的类型
        if (!empty($configData)) {
            // 如果是字符串，尝试解析JSON
            if (is_string($configData)) {
                $parsedData = json_decode($configData, true);
                if (is_array($parsedData)) {
                    $config = $parsedData;
                }
            }
            // 如果已经是数组，直接使用
            else if (is_array($configData)) {
                $config = $configData;
            }
        }

        // 如果没有获取到有效配置，使用默认值
        if (!isset($config['refund_publicity_period_days'])) {
            $config['refund_publicity_period_days'] = 90;
        }

        return [
            'notice' => '<div> <img src="'.$notice.'" /></div>',
            'publicity_period_days' => $config['refund_publicity_period_days']
        ];
    }

    /**
     * @notes 检查代理退款条件
     * @param int $user_id 用户ID
     * @return array 检查结果
     */
    public static function checkAgentRefundCondition($user_id)
    {
        // 初始化返回结果，格式与/shopapi/shop/applyDeactivate接口一致
        $result = [
            'code' => 1,
            'show' => 0,
            'msg' => '账号注销条件检查结果',
            'data' => [
                'can_deactivate' => true
            ]
        ];

        try {
            // 1. 检查是否有保证金记录
            $deposit = Db::name('agent_merchantfees')
                ->where('user_id', $user_id)
                ->where('status', 'in', [1, 2]) // 1已支付(公示期), 2公示期结束(可退)
                ->order('id', 'desc')
                ->find();

            if (!$deposit) {
                $result['data']['has_deposit'] = [
                    'status' => false,
                    'message' => '未找到有效的保证金支付记录'
                ];
                $result['data']['can_deactivate'] = false;
            } else if ($deposit['status'] == 1 && time() < $deposit['publicity_period_end_time']) {
                // 如果在公示期内，不能退款
                $endDate = date('Y-m-d H:i:s', $deposit['publicity_period_end_time']);
                $result['data']['has_deposit'] = [
                    'status' => false,
                    'message' => '保证金仍在公示期，请于 ' . $endDate . ' 后再申请退款'
                ];
                $result['data']['can_deactivate'] = false;
            } else {
                $result['data']['has_deposit'] = [
                    'status' => true,
                    'message' => '保证金状态有效'
                ];
            }

            // 2. 检查agent_order表中是否有待返佣的订单
            $pendingCommissionCount = Db::name('agent_order')
                ->where('user_id', $user_id)
                ->where('status', 1) // 待返佣
                ->count();

            $result['data']['pending_commission'] = [
                'status' => $pendingCommissionCount <= 0,
                'message' => $pendingCommissionCount <= 0 ? '没有待返佣的订单' : '有 ' . $pendingCommissionCount . ' 个待返佣的订单',
                'count' => $pendingCommissionCount
            ];

            if ($pendingCommissionCount > 0) {
                $result['data']['can_deactivate'] = false;
            }

            // 3. 检查agent_order表中是否有冻结的订单
            $frozenCommissionCount = Db::name('agent_order')
                ->where('user_id', $user_id)
                ->where('status', 3) // 冻结
                ->count();

            $result['data']['frozen_commission'] = [
                'status' => $frozenCommissionCount <= 0,
                'message' => $frozenCommissionCount <= 0 ? '没有冻结的订单' : '有 ' . $frozenCommissionCount . ' 个冻结的订单',
                'count' => $frozenCommissionCount
            ];

            if ($frozenCommissionCount > 0) {
                $result['data']['can_deactivate'] = false;
            }

            // 4. 检查agent_withdrawal表中是否有待审核、审核通过待打款或打款中的记录
            $pendingWithdrawalCount = Db::name('agent_withdrawal')
                ->where('user_id', $user_id)
                ->whereIn('status', [0, 1, 3]) // 0待审核, 1审核通过待打款, 3打款中
                ->count();

            $result['data']['pending_withdrawal'] = [
                'status' => $pendingWithdrawalCount <= 0,
                'message' => $pendingWithdrawalCount <= 0 ? '没有待处理的提现申请' : '有 ' . $pendingWithdrawalCount . ' 笔提现申请正在处理中',
                'count' => $pendingWithdrawalCount
            ];

            if ($pendingWithdrawalCount > 0) {
                $result['data']['can_deactivate'] = false;
            }
            $doc_path=Db::name('help')->where('id',29)->value('document_path');
            $result['data']['doc_url']=$doc_path=UrlServer::getFileUrl($doc_path);
            return $result;
        } catch (\Exception $e) {
            return [
                'code' => 0,
                'show' => 1,
                'msg' => '检查条件时发生错误: ' . $e->getMessage(),
                'data' => [
                    'can_deactivate' => false,
                    'error' => $e->getMessage(),
                    'doc_url'=>$doc_path
                ]
            ];
        }
    }



    /**
     * @notes 获取代理保证金退款申请状态
     * @param int $user_id 用户ID
     * @return array 申请状态信息
     */
    public static function getAgentRefundStatus($user_id)
    {
        try {
            $depositOrder = Db::name('agent_merchantfees')
                ->where('user_id', $user_id)
                ->where('payment_date','>',0)
                ->order('id', 'desc') // 获取最新的保证金订单
                ->find();
            $deposit_change = Db::name('agent_deposit_details')
                ->where('user_id', $user_id)
                ->sum('deposit_change');

            if (!$depositOrder) {

                return [
                    'has_deposit' => false,
                    'message' => '未找到保证金记录'
                ];
            }

            $statusMap = [
                0 => '未支付',
                1 => '已支付',
                2 => '公示期结束(可退)',
                3 => '退款申请中(公示期)',
                4 => '已退款',
                5 => '退款失败',
                6 => '补缴'
            ];

            $result = [
                'has_deposit' => true,
                'deposit_id' => $depositOrder['id'],
                'amount' => $depositOrder['amount']+ $deposit_change,
                'old_money' => $depositOrder['old_money'],
                'bu_money'=>abs($depositOrder['old_money']-$depositOrder['amount']),
                'status' => $depositOrder['status'],
                'status_text' => $statusMap[$depositOrder['status']] ?? '未知状态',
                'payment_date' => date('Y-m-d H:i:s', $depositOrder['payment_date']),
                'can_apply_refund' => false,
                'can_confirm_refund' => false,
                'days_remaining' => 0,
                'message' => ''
            ];
            //如果存在补缴,且状态为1  则状态改为6 即补缴保证金的状态
            if($result['bu_money']>0 && $result['status']==1){
                $depositOrder['status']=$result['status']=6;
            }


            $result['agent_info']=Db::name('agent')->where('user_id',$user_id)->find();

            //查询是否有退款申请记录
            $refund=Db::name('common_refund')->where('source_id', $depositOrder['id'])->where('refund_type', 4)->find();
            if($refund && $result['status']==3){
                $depositOrder['status']=$result['status']=4;
            }
            // 根据状
            //+v 态设置不同的信息
            switch ($depositOrder['status']) {
                case 0: // 未支付
                    $result['message'] = '保证金未支付，无法申请退款';
                    break;

                case 1: // 已支付(公示期)
                    if (time() < $depositOrder['publicity_period_end_time']) {
                        $daysRemaining = ceil(($depositOrder['publicity_period_end_time'] - time()) / 86400);
                        $result['days_remaining'] = $daysRemaining;
                        $result['message'] = "保证金仍在公示期，还需等待{$daysRemaining}天才能申请退款";
                    } else {
                        $result['can_apply_refund'] = true;
                        $result['message'] = '公示期已结束，可以申请退款';
                    }
                    break;

                case 2: // 公示期结束(可退)
                    $result['can_apply_refund'] = true;
                    $result['message'] = '可以申请退款';
                    break;

                case 3: // 退款申请中
                    if ($depositOrder['refund_publicity_end_time'] > time()) {
                        $daysRemaining = ceil(($depositOrder['refund_publicity_end_time'] - time()) / 86400);
                        $result['days_remaining'] = $daysRemaining;
                        $result['message'] = "退款申请审核中，还需等待{$daysRemaining}天公示期结束";
                    } else {
                        $result['can_confirm_refund'] = true;
                        $result['message'] = '公示期已结束，可以确认退款';
                        $result['status'] = 2; // 公示期结束(可退)
                    }
                    break;

                case 4: // 已退款
                    $result['message'] = '保证金已退款,预计1-7个工作日到账，请耐心等待';
                    break;

                case 5: // 退款失败
                    $result['message'] = '退款失败，请联系客服处理';
                    break;
                case 6:

                    $result['message'] = '保证金补缴中，如需更改，请联系客服';
                    break;
            }

            return $result;
        } catch (\Exception $e) {
            return [
                'has_deposit' => false,
                'message' => '获取状态失败: ' . $e->getMessage()
            ];
        }
    }

    /**
     * @notes 撤销代理保证金退款申请
     * @param int $user_id 用户ID
     * @return bool|string|array
     */
    public static function cancelAgentDepositRefund($user_id)
    {
        try {
            // 获取退款申请记录（状态为3-退款申请中 或 2-公示期结束可退）
            $depositOrder = Db::name('agent_merchantfees')
                ->where('user_id', $user_id)
                ->where('status', 'in', [2, 3]) // 2-公示期结束可退, 3-退款申请中
                ->order('id', 'desc')
                ->find();

            if (!$depositOrder) {
                return [
                    'code' => 0,
                    'show' => 1,
                    'msg' => '未找到有效的保证金记录',
                    'data' => []
                ];
            }

            // 查询是否已经有退款记录
            $refund = Db::name('common_refund')->where('source_id', $depositOrder['id'])->where('refund_type', 4)->find();
            if ($refund && $refund['refund_status'] != 0) {
                return [
                    'code' => 0,
                    'show' => 1,
                    'msg' => '退款已处理，无法撤销',
                    'data' => []
                ];
            }

            // 开始事务
            Db::startTrans();

            // 如果状态为3（退款申请中），需要恢复状态为2（公示期结束可退）
            if ($depositOrder['status'] == 3) {
                // 更新保证金状态为公示期结束(可退)
                Db::name('agent_merchantfees')
                    ->where('id', $depositOrder['id'])
                    ->update([
                        'status' => 1, // 公示期结束(可退)
                        'refund_request_time' => 0, // 清空退款申请时间
                        'refund_publicity_end_time' => 0 // 清空退款公示期结束时间
                    ]);

                // 如果有退款记录，删除退款记录
                if ($refund) {
                    Db::name('common_refund')
                        ->where('id', $refund['id'])
                        ->delete();
                }
            }

            // 根据状态记录不同的操作日志
            $action = $depositOrder['status'] == 3 ? '撤销退款' : '取消退款';
            $remark = $depositOrder['status'] == 3 ? '用户撤销保证金退款申请' : '用户取消保证金退款意向';

            $log = [
                'deposit_id' => $depositOrder['id'],
                'user_id' => $user_id,
                'admin_id' => 0, // 用户操作，非管理员
                'action' => $action,
                'remark' => $remark,
                'create_time' => time()
            ];

            // // 记录操作日志
            // Db::name('agent_deposit_log')->insert($log);

            // 同时记录到通用日志表
            Db::name('log')->insert([
               'type'=>'refund_log','log'=>json_encode($log)
            ]);

            Db::commit();

            // 根据状态返回不同的成功消息
            $successMsg = $depositOrder['status'] == 3 ? '撤销退款申请成功' : '取消退款意向成功';

            return [
                'code' => 1,
                'show' => 0,
                'msg' => $successMsg,
                'data' => []
            ];
        } catch (\Exception $e) {
            Db::rollback();
            return [
                'code' => 0,
                'show' => 1,
                'msg' => '撤销退款申请失败：' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * @notes 确认代理保证金退款
     * @param int $user_id 用户ID
     * @return bool|string
     */
    public static function confirmAgentRefund($user_id)
    {
        try {
            // 获取退款申请记录
            $depositOrder = Db::name('agent_merchantfees')
                ->where('user_id', $user_id)
                ->where('status', 3) // 退款申请中
                ->order('id', 'desc')
                ->find();

            if (!$depositOrder) {
                return '未找到有效的退款申请记录';
            }
            //查询是否有退款申请记录
            $refund=Db::name('common_refund')->where('source_id', $depositOrder['id'])->where('refund_type', 4)->find();
            if($refund){
                return '退款银联机构处理中，请勿重复确认';
            }

            // 检查是否已过公示期
            if (time() < $depositOrder['refund_publicity_end_time']) {
                $daysRemaining = ceil(($depositOrder['refund_publicity_end_time'] - time()) / 86400);
                return "退款申请仍在公示期，还需等待{$daysRemaining}天才能确认退款";
            }
            //获取保证金余额
            // 获取所有明细记录的变动金额总和
            $changes_sum = Db::name('agent_deposit_details')
                ->where('deposit_id', $depositOrder['id'])
                ->sum('deposit_change');

            // 计算当前余额 = 初始金额 + 变动金额总和
            $current_balance = $depositOrder['amount'] + floatval($changes_sum);

            $cha=max(0, $current_balance); // 确保余额不为负数
            if($cha == 0){
                return '保证金余额不足，无法退款';
            }

            // 更新保证金记录状态为已退款
            // 创建退款记录
            $refundData = [
                'refund_sn' => createSn('common_refund', 'refund_sn'),
                'refund_type' => 4, // 4-代理保证金
                'source_id' => $depositOrder['id'],
                'user_id' => $user_id,
                'agent_id' => Db::name('agent')->where('user_id', $user_id)->where('del', 0)->value('id') ?: 0,
                'refund_amount' => $cha,
                'total_amount' => $depositOrder['amount'],
                'payment_method' => 'wechat', // 假设都是微信支付
                'transaction_id' => $depositOrder['transaction_id'], // 可能需要从支付记录中获取
                'refund_status' => 0, // 0-退款中
                'refund_msg' => '代理保证金申请退款',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];
            //暂停收益,将他的直推用户变成平台的

            Db::startTrans();
            try {
                // 插入退款记录
                Db::name('common_refund')->insertGetId($refundData);
                Db::name('agent_relationships')->where('sponsor_id', $user_id)->update(['sponsor_id' => 0]);
                // 给平台管理员发送消息


                Db::commit();
                return true;
            } catch (\Exception $e) {
                Db::rollback();
                return '确认退款失败: ' . $e->getMessage();
            }
        } catch (\Exception $e) {
            return '确认退款失败: ' . $e->getMessage();
        }
    }

    /**
     * @notes 检查代理退款状态详情
     * @param int $user_id 用户ID
     * @return array 详细的状态信息
     */
    public static function checkAgentRefundStatus($user_id)
    {
        // 初始化返回结果，格式与/shopapi/shop/applyDeactivate接口一致
        $result = [
            'code' => 1,
            'show' => 0,
            'msg' => '账号注销条件检查结果',
            'data' => [
                'can_deactivate' => false,
                'status' => [
                    'has_agent' => false,           // 是否是代理
                    'has_deposit' => false,         // 是否有保证金记录
                    'deposit_in_publicity' => false, // 保证金是否在公示期
                    'can_apply_refund' => false,    // 是否可以申请退款
                    'has_pending_refund' => false,  // 是否有待处理的退款申请
                    'refund_in_publicity' => false, // 退款申请是否在公示期
                    'can_confirm_refund' => false,  // 是否可以确认退款
                ],
                'deposit_info' => null,             // 保证金信息
                'agent_info' => null,               // 代理信息
                'messages' => [],                   // 详细说明信息
                'next_steps' => []                  // 下一步操作建议
            ]
        ];

        try {
            // 1. 检查用户是否是代理
            $agent = Db::name('agent')
                ->where('user_id', $user_id)
                ->where('del', 0) // 只检查未删除的代理
                ->find();

            if (!$agent) {
                $result['data']['messages'][] = '您不是代理，无法申请退款';
                $result['data']['next_steps'][] = '如需成为代理，请联系客服或通过代理申请入口提交申请';
                return $result;
            }

            $result['data']['status']['has_agent'] = true;
            $result['data']['agent_info'] = [
                'id' => $agent['id'],
                'level' => $agent['level'],
                'level_text' => $agent['level'] == 1 ? '一级代理' : '二级代理',
                'is_freeze' => $agent['is_freeze'],
                'create_time' => date('Y-m-d H:i:s', $agent['create_time']),
                'distribution_start_time' => date('Y-m-d H:i:s', $agent['distribution_start_time']),
                'distribution_end_time' => date('Y-m-d H:i:s', $agent['distribution_end_time'])
            ];

            // 2. 检查代理状态
            if ($agent['is_freeze'] == 1) {
                $result['data']['messages'][] = '您的代理账号已被冻结，无法申请退款';
                $result['data']['next_steps'][] = '请联系客服了解冻结原因并处理';
                return $result;
            }

            // 3. 检查是否有保证金记录
            $deposit = Db::name('agent_merchantfees')
                ->where('user_id', $user_id)
                ->order('id', 'desc')
                ->find();

            if (!$deposit) {
                $result['data']['messages'][] = '未找到保证金记录，无法申请退款';
                $result['data']['next_steps'][] = '如有疑问，请联系客服核实保证金缴纳情况';
                return $result;
            }

            $result['data']['status']['has_deposit'] = true;
            $result['data']['deposit_info'] = [
                'id' => $deposit['id'],
                'amount' => $deposit['amount'],
                'status' => $deposit['status'],
                'status_text' => self::getDepositStatusText($deposit['status']),
                'payment_date' => date('Y-m-d H:i:s', $deposit['payment_date'])
            ];

            // 4. 根据保证金状态提供详细信息
            switch ($deposit['status']) {
                case 0: // 未支付
                    $result['data']['messages'][] = '保证金未支付，无法申请退款';
                    $result['data']['next_steps'][] = '请先完成保证金支付';
                    break;

                case 1: // 已支付(公示期)
                    $result['data']['status']['deposit_in_publicity'] = true;
                    if (time() < $deposit['publicity_period_end_time']) {
                        $daysRemaining = ceil(($deposit['publicity_period_end_time'] - time()) / 86400);
                        $result['data']['messages'][] = "保证金仍在公示期，还需等待{$daysRemaining}天才能申请退款";
                        $result['data']['next_steps'][] = "请于 " . date('Y-m-d H:i:s', $deposit['publicity_period_end_time']) . " 后再申请退款";
                    } else {
                        $result['data']['status']['can_apply_refund'] = true;
                        $result['data']['can_deactivate'] = true;
                        $result['data']['messages'][] = '公示期已结束，可以申请退款';
                        $result['data']['next_steps'][] = '请前往"申请退款"页面提交退款申请';
                    }
                    break;

                case 2: // 公示期结束(可退)
                    $result['data']['status']['can_apply_refund'] = true;
                    $result['data']['can_deactivate'] = true;
                    $result['data']['messages'][] = '保证金可以申请退款';
                    $result['data']['next_steps'][] = '请前往"申请退款"页面提交退款申请';
                    break;

                case 3: // 退款申请中
                    $result['data']['status']['has_pending_refund'] = true;
                    if (time() < $deposit['refund_publicity_end_time']) {
                        $result['data']['status']['refund_in_publicity'] = true;
                        $daysRemaining = ceil(($deposit['refund_publicity_end_time'] - time()) / 86400);
                        $result['data']['messages'][] = "退款申请审核中，还需等待{$daysRemaining}天公示期结束";
                        $result['data']['next_steps'][] = "请于 " . date('Y-m-d H:i:s', $deposit['refund_publicity_end_time']) . " 后确认退款";
                    } else {
                        $result['data']['status']['can_confirm_refund'] = true;
                        $result['data']['can_deactivate'] = true;
                        $result['data']['messages'][] = '公示期已结束，可以确认退款';
                        $result['data']['next_steps'][] = '请前往"确认退款"页面完成退款操作';
                    }
                    break;

                case 4: // 已退款
                    $result['data']['messages'][] = '保证金已退款';
                    $result['data']['next_steps'][] = '退款已完成，无需进一步操作';
                    break;

                case 5: // 退款失败
                    $result['data']['messages'][] = '退款失败，请联系客服处理';
                    $result['data']['next_steps'][] = '请联系客服了解退款失败原因并协助处理';
                    break;
            }

            // 5. 如果可以申请退款，检查其他条件
            if ($result['data']['status']['can_apply_refund'] || $result['data']['status']['can_confirm_refund']) {
                // 检查是否有待返佣订单
                $pendingCommissionCount = Db::name('agent_order')
                    ->where('user_id', $user_id)
                    ->where('status', 1) // 待返佣
                    ->count();

                if ($pendingCommissionCount > 0) {
                    $result['data']['can_deactivate'] = false;
                    $result['data']['messages'][] = "您有{$pendingCommissionCount}个订单待返佣，请等待返佣完成后再申请退款";
                    $result['data']['next_steps'][] = "请等待订单返佣完成后再操作";
                }

                // 检查是否有冻结订单
                $frozenCommissionCount = Db::name('agent_order')
                    ->where('user_id', $user_id)
                    ->where('status', 3) // 冻结
                    ->count();

                if ($frozenCommissionCount > 0) {
                    $result['data']['can_deactivate'] = false;
                    $result['data']['messages'][] = "您有{$frozenCommissionCount}个订单被冻结，请联系客服处理后再申请退款";
                    $result['data']['next_steps'][] = "请联系客服处理冻结订单";
                }

                // 检查是否有待处理的提现申请
                $pendingWithdrawalCount = Db::name('agent_withdrawal')
                    ->where('user_id', $user_id)
                    ->whereIn('status', [0, 1, 3]) // 0待审核, 1审核通过待打款, 3打款中
                    ->count();

                if ($pendingWithdrawalCount > 0) {
                    $result['data']['can_deactivate'] = false;
                    $result['data']['messages'][] = "您有{$pendingWithdrawalCount}笔提现申请正在处理中，请等待处理完成后再申请退款";
                    $result['data']['next_steps'][] = "请等待提现申请处理完成后再操作";
                }
            }

            return $result;
        } catch (\Exception $e) {
            return [
                'code' => 0,
                'show' => 1,
                'msg' => '检查状态时发生错误: ' . $e->getMessage(),
                'data' => [
                    'can_deactivate' => false,
                    'status' => [
                        'has_agent' => false,
                        'has_deposit' => false,
                        'deposit_in_publicity' => false,
                        'can_apply_refund' => false,
                        'has_pending_refund' => false,
                        'refund_in_publicity' => false,
                        'can_confirm_refund' => false,
                    ],
                    'deposit_info' => null,
                    'agent_info' => null,
                    'messages' => ['检查状态时发生错误: ' . $e->getMessage()],
                    'next_steps' => ['请稍后再试或联系客服处理']
                ]
            ];
        }
    }
}
