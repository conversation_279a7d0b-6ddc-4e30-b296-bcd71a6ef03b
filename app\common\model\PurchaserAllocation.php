<?php
namespace app\common\model;

use app\common\basics\Models;

/**
 * 采购人员分配记录模型
 * Class PurchaserAllocation
 * @package app\common\model
 */
class PurchaserAllocation extends Models
{
    /**
     * 关联采购人员
     * @return \think\model\relation\BelongsTo
     */
    public function purchaser()
    {
        return $this->belongsTo(Purchaser::class, 'purchaser_id', 'id');
    }
    
    /**
     * 关联商家
     * @return \think\model\relation\BelongsTo
     */
    public function shop()
    {
        return $this->belongsTo(\app\common\model\shop\Shop::class, 'shop_id', 'id');
    }
    
    /**
     * 关联套餐订单
     * @return \think\model\relation\BelongsTo
     */
    public function packageOrder()
    {
        return $this->belongsTo(PurchaserPackageOrder::class, 'package_order_id', 'id');
    }
    
    /**
     * 获取状态文本
     * @param $value
     * @param $data
     * @return string
     */
    public function getStatusTextAttr($value, $data)
    {
        if ($data['status'] == 0) {
            return '失效';
        }
        
        if ($data['expire_time'] < time()) {
            return '已过期';
        }
        
        return '有效';
    }
}
