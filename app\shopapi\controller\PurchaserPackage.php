<?php
namespace app\shopapi\controller;

use app\api\logic\PayLogic;
use app\common\basics\ShopApi;
use app\common\enum\PayEnum;
use app\common\server\JsonServer;
use app\shopapi\logic\PurchaserPackageLogic;
use think\facade\Log;

/**
 * 采购套餐控制器
 * Class PurchaserPackage
 * @package app\shopapi\controller
 */
class PurchaserPackage extends ShopApi
{
    /**
     * 获取套餐列表
     * @return \think\response\Json
     */
    public function packageList()
    {
        try {
            $packages = PurchaserPackageLogic::getPackageList();
            return JsonServer::success('获取成功', $packages);
        } catch (\Exception $e) {
            Log::error('获取采购套餐列表失败: ' . $e->getMessage());
            return JsonServer::error('获取套餐列表失败');
        }
    }
    
    /**
     * 创建采购套餐购买订单
     * @return \think\response\Json
     */
    public function createOrder()
    {
        try {
            $params = $this->request->post();
            
            // 验证参数
            if (empty($params['package_id'])) {
                return JsonServer::error('请选择套餐');
            }
            
            // 添加商家ID
            $params['shop_id'] = $this->shop_id;
            
            $result = PurchaserPackageLogic::createOrder($params);
            
            if ($result === false) {
                return JsonServer::error(PurchaserPackageLogic::getError() ?: '创建订单失败');
            }
            
            return JsonServer::success('订单创建成功', $result);
            
        } catch (\Exception $e) {
            Log::error('创建采购套餐订单失败: ' . $e->getMessage());
            return JsonServer::error('创建订单失败');
        }
    }
    
    /**
     * 获取订单详情
     * @return \think\response\Json
     */
    public function orderDetail()
    {
        try {
            $orderSn = $this->request->param('order_sn');
            
            if (empty($orderSn)) {
                return JsonServer::error('订单号不能为空');
            }
            
            $result = PurchaserPackageLogic::getOrderDetail($orderSn, $this->shop_id);
            
            if ($result === false) {
                return JsonServer::error(PurchaserPackageLogic::getError() ?: '获取订单详情失败');
            }
            
            return JsonServer::success('获取成功', $result);
            
        } catch (\Exception $e) {
            Log::error('获取采购套餐订单详情失败: ' . $e->getMessage());
            return JsonServer::error('获取订单详情失败');
        }
    }
    
    /**
     * 获取订单列表
     * @return \think\response\Json
     */
    public function orderList()
    {
        try {
            $params = $this->request->get();
            $params['page_no'] = $this->page_no;
            $params['page_size'] = $this->page_size;
            
            $result = PurchaserPackageLogic::getOrderList($this->shop_id, $params);
            
            return JsonServer::success('获取成功', $result);
            
        } catch (\Exception $e) {
            Log::error('获取采购套餐订单列表失败: ' . $e->getMessage());
            return JsonServer::error('获取订单列表失败');
        }
    }
    
    /**
     * 取消订单
     * @return \think\response\Json
     */
    public function cancelOrder()
    {
        try {
            $orderSn = $this->request->post('order_sn');
            
            if (empty($orderSn)) {
                return JsonServer::error('订单号不能为空');
            }
            
            $result = PurchaserPackageLogic::cancelOrder($orderSn, $this->shop_id);
            
            if ($result === false) {
                return JsonServer::error(PurchaserPackageLogic::getError() ?: '取消订单失败');
            }
            
            return JsonServer::success('订单已取消');
            
        } catch (\Exception $e) {
            Log::error('取消采购套餐订单失败: ' . $e->getMessage());
            return JsonServer::error('取消订单失败');
        }
    }
    
    /**
     * 支付采购套餐订单
     * @return \think\response\Json
     */
    public function payOrder()
    {
        try {
            $params = $this->request->post();
            
            // 验证参数
            if (empty($params['order_sn'])) {
                return JsonServer::error('订单号不能为空');
            }
            
            if (empty($params['pay_way'])) {
                return JsonServer::error('请选择支付方式');
            }
            
            // 验证订单是否存在且未支付
            $orderDetail = PurchaserPackageLogic::getOrderDetail($params['order_sn'], $this->shop_id);
            if ($orderDetail === false) {
                return JsonServer::error('订单不存在');
            }
            
            if ($orderDetail['pay_status'] == 1) {
                return JsonServer::error('订单已支付');
            }
            
            // 调用支付逻辑
            $payWay = $params['pay_way'];
            $result = null;
            
            switch ($payWay) {
                case PayEnum::WECHAT_PAY: // 微信支付
                    $result = PayLogic::wechatPay($params['order_sn'], 'PurchaserPackage', $this->client);
                    break;
                case PayEnum::ALI_PAY: // 支付宝支付
                    $result = PayLogic::aliPay($params['order_sn'], 'PurchaserPackage', $this->client);
                    
                    if (app()->isDebug()) {
                        Log::write($result, 'purchaser_package_alipay');
                    }
                    
                    $data = [
                        'code' => 10001,
                        'msg' => '发起成功',
                        'data' => $result,
                        'show' => 0,
                    ];
                    return json($data);
                default:
                    return JsonServer::error('不支持的支付方式');
            }
            
            return $result;
            
        } catch (\Exception $e) {
            Log::error('采购套餐支付失败: ' . $e->getMessage());
            return JsonServer::error('支付失败：' . $e->getMessage());
        }
    }
    
    /**
     * 获取已分配的采购人员
     * @return \think\response\Json
     */
    public function allocatedPurchasers()
    {
        try {
            $result = PurchaserPackageLogic::getAllocatedPurchasers($this->shop_id);
            
            return JsonServer::success('获取成功', $result);
            
        } catch (\Exception $e) {
            Log::error('获取已分配采购人员失败: ' . $e->getMessage());
            return JsonServer::error('获取已分配采购人员失败');
        }
    }
}
