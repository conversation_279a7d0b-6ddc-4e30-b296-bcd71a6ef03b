<?php
require __DIR__ . '/vendor/autoload.php';

use think\facade\Db;

// The list of IDs to delete
$ids = [ 121,115,87,180,171];

try {
    // Manually configure the database connection
    $dbConfig = [
        'type'            => 'mysql',
        'hostname'        => '127.0.0.1',
        'database'        => 'kshop',
        'username'        => 'kshop',
        'password'        => 'DetPwbd6YrtMasHf',
        'hostport'        => '3306',
        'charset'         => 'utf8mb4',
        'prefix'          => 'ls_',
        'break_reconnect' => true,
    ];

    \think\facade\Db::setConfig(['default' => 'mysql', 'connections' => ['mysql' => $dbConfig]]);

    $relatedTables = [
        'ls_activity_area_goods', 'ls_ad', 'ls_ad_order', 'ls_article', 'ls_bargain',
        'ls_cart', 'ls_chat_record', 'ls_chat_relation', 'ls_coupon',
        'ls_face_sheet_sender', 'ls_face_sheet_template', 'ls_file', 'ls_file_cate',
        'ls_free_shipping_config', 'ls_free_shipping_region', 'ls_freight', 'ls_goods',
        'ls_goods_click', 'ls_goods_collect', 'ls_goods_comment', 'ls_goods_footprint',
        'ls_jcai_activity', 'ls_jcai_found', 'ls_jcai_join', 'ls_kefu', 'ls_kefu_lang',
        'ls_live_goods', 'ls_live_room', 'ls_order', 'ls_order_goods', 'ls_order_invoice',
        'ls_printer', 'ls_printer_config', 'ls_seckill_goods', 'ls_shop_account_log',
        'ls_shop_ad', 'ls_shop_admin', 'ls_shop_alipay', 'ls_shop_bank',
        'ls_shop_deactivate_apply', 'ls_shop_deposit', 'ls_shop_deposit_details',
        'ls_shop_follow', 'ls_shop_footprint', 'ls_shop_goods_category',
        'ls_shop_icon_config', 'ls_shop_inspection', 'ls_shop_merchantfees',
        'ls_shop_operation_log', 'ls_shop_role', 'ls_shop_session', 'ls_shop_settlement',
        'ls_shop_stat', 'ls_shop_withdrawal', 'ls_supplier', 'ls_team_activity',
        'ls_team_found', 'ls_team_join', 'ls_verification'
    ];

    Db::startTrans();
    try {
        $deletedShopCount = 0;
        foreach ($ids as $shopId) {
            foreach ($relatedTables as $table) {
                Db::name(str_replace('ls_', '', $table))->where('shop_id', $shopId)->delete();
            }
            $result = Db::name('shop')->where('id', $shopId)->delete();
            if ($result) {
                $deletedShopCount++;
            }
        }

        Db::commit();
        echo "Successfully deleted " . $deletedShopCount . " shops and their related data.\n";
    } catch (\Exception $e) {
        Db::rollback();
        throw $e;
    }
} catch (\Exception $e) {
    echo "An error occurred: " . $e->getMessage() . "\n";
}
