-- ----------------------------
-- Table structure for ls_purchaser
-- ----------------------------
DROP TABLE IF EXISTS `ls_purchaser`;
CREATE TABLE `ls_purchaser` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '采购人员姓名',
  `mobile` varchar(20) NOT NULL COMMENT '手机号',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `level` tinyint(1) NOT NULL DEFAULT '1' COMMENT '采购等级(1=初级, 2=中级, 3=高级)',
  `max_shops` int(11) DEFAULT NULL COMMENT '最大可分配商家数量(NULL=无限制)',
  `assigned_shops` int(11) NOT NULL DEFAULT '0' COMMENT '已分配商家数量',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态(0=禁用, 1=启用)',
  `create_time` int(11) DEFAULT NULL,
  `update_time` int(11) DEFAULT NULL,
  `delete_time` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `level` (`level`) USING BTREE,
  KEY `status` (`status`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='采购人员表';

-- ----------------------------
-- Table structure for ls_purchaser_allocation
-- ----------------------------
DROP TABLE IF EXISTS `ls_purchaser_allocation`;
CREATE TABLE `ls_purchaser_allocation` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `shop_id` int(11) NOT NULL COMMENT '商家ID',
  `purchaser_id` int(11) NOT NULL COMMENT '采购人员ID',
  `package_order_id` int(11) NOT NULL COMMENT '套餐订单ID',
  `allocated_time` int(11) NOT NULL COMMENT '分配时间',
  `expire_time` int(11) NOT NULL COMMENT '到期时间',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态(0=失效, 1=有效)',
  `create_time` int(11) DEFAULT NULL,
  `update_time` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `shop_id` (`shop_id`) USING BTREE,
  KEY `purchaser_id` (`purchaser_id`) USING BTREE,
  KEY `package_order_id` (`package_order_id`) USING BTREE,
  KEY `expire_time` (`expire_time`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='采购人员分配记录表';

-- ----------------------------
-- 添加事务ID字段到采购套餐订单表
-- ----------------------------
ALTER TABLE `ls_purchaser_package_order` ADD COLUMN `transaction_id` varchar(64) DEFAULT NULL COMMENT '第三方支付流水号' AFTER `pay_time`;

-- ----------------------------
-- 初始化一些测试数据
-- ----------------------------
INSERT INTO `ls_purchaser` (`name`, `mobile`, `email`, `level`, `max_shops`, `assigned_shops`, `status`, `create_time`, `update_time`) VALUES
('张采购', '13800138001', '<EMAIL>', 1, 10, 0, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('李采购', '13800138002', '<EMAIL>', 1, 10, 0, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('王采购', '13800138003', '<EMAIL>', 2, 8, 0, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('赵采购', '13800138004', '<EMAIL>', 2, 8, 0, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('刘采购', '13800138005', '<EMAIL>', 3, 5, 0, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('陈采购', '13800138006', '<EMAIL>', 3, 5, 0, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());
