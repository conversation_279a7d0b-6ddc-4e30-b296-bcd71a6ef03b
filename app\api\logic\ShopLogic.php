<?php
namespace app\api\logic;

use app\common\basics\Logic;
use app\common\enum\GoodsEnum;
use app\common\enum\ShopAdEnum;
use app\common\enum\ShopEnum;
use app\common\logic\QrCodeLogic;
use app\common\model\dev\DevRegion;
use app\common\model\shop\ShopAd;
use app\common\server\ConfigServer;
use app\common\server\UrlServer;
use app\common\model\goods\Goods;
use app\common\model\order\OrderGoods;
use app\common\model\shop\Shop;
use app\common\model\shop\ShopFollow;
use think\facade\Db;
use think\facade\Event;



class ShopLogic extends Logic
{
    /**
     * 获取店铺信息
     */
    public static function getShopInfo($shopId, $userId, $params = [])
    {
        // 记录统计信息(访问商铺用户量)
        Event::listen('ShopStat', 'app\common\listener\ShopStat');
        event('ShopStat', $shopId);

        $where = [
            'del' => 0,
            'id' => $shopId
        ];
        $field = [
            'id','yan_level','cid','shop_label','jcshop_vip','is_recommend','create_time', 'name', 'logo', 'background',
            'type', 'score', 'star', 'intro',
            'visited_num', 'mobile','cover', 'banner', 'is_freeze',
            'is_run', 'expire_time','return_rate',
            'province_id', 'city_id', 'district_id', 'address',
            'run_start_time', 'run_end_time','weekdays','update_time'
        ];
        $shop = Shop::field($field)
            ->where($where)
            ->append([ 'type_desc', 'is_expire' ])
            ->findOrEmpty();
        if($shop->isEmpty()) {
            return [];
        }else{
            $shop = $shop->toArray();
        }
        //
        $shop['logo']           = UrlServer::getFileUrl($shop['logo'] ? : ShopEnum::DEFAULT_LOGO);
        $shop['background']     = UrlServer::getFileUrl($shop['background'] ? : ShopEnum::DEFAULT_BG);
        $shop['cover']          = UrlServer::getFileUrl($shop['cover'] ? :ShopEnum::DEFAULT_COVER);
        $shop['banner']         = UrlServer::getFileUrl($shop['banner'] ? : ShopEnum::DEFAULT_BANNER);
        $shop['run_start_time'] = $shop['run_start_time'] ? date('H:i:s', $shop['run_start_time']) : '';
        $shop['run_end_time']   = $shop['run_end_time'] ? date('H:i:s', $shop['run_end_time']) : '';
        $shop['province']       = DevRegion::getAreaName($shop['province_id']);
        $shop['city']           = DevRegion::getAreaName($shop['city_id']);
        $shop['district']       = DevRegion::getAreaName($shop['district_id']);
        $yan_arr=[1=>'权威验厂',2=>'权威验商',3=>'权威品牌'];
        $shop['qr_code']        = (new QrCodeLogic)->shopQrCode($shop['id'], $params['terminal'] ?? '');
        $jc_vip='/uploads/images/20250109/202501091712370e7414006.png';
        $tui='/uploads/images/20250109/20250109172215b02214651.png';
        $shop['jcshop_vip_image']=$shop['jcshop_vip']?UrlServer::getFileUrl($jc_vip):'';
        $shop['tui_image']=$shop['is_recommend']==1?UrlServer::getFileUrl($tui):'';
         $shopLevel = null;
            if (!empty($shop['yan_level'])) {
                $shopLevel = Db::name('shop_level')->where('id', $shop['yan_level'])->find();
            }

            // 设置等级图片和名称，确保安全访问
            $shop['yan_level_image'] = ($shopLevel && !empty($shopLevel['image'])) ? UrlServer::getFileUrl($shopLevel['image']) : '';
            $shop['yan_level'] = ($shopLevel && !empty($shopLevel['name'])) ? $shopLevel['name'] : '';
            $shop['shop_label']=  $shop['shop_label']?explode(',',$shop['shop_label']):[];
       
        // 在售商品
        // 销售中商品：未删除/审核通过/已上架
        $onSaleWhere = [
            ['del', '=', GoodsEnum::DEL_NORMAL],  // 未删除
            ['status', '=', GoodsEnum::STATUS_SHELVES], // 上架中
            ['audit_status', '=', GoodsEnum::AUDIT_STATUS_OK], // 审核通过
        ];
        $shop['on_sale_count'] = Goods::where($onSaleWhere)->where('shop_id', $shopId)->count();
        $shop['shop_time'] =(int)date('Y',time())-(int)date('Y',strtotime($shop['create_time']));
        if($shop['shop_time']<1){
            $shop['shop_time']=1;
        }else{
            $shop['shop_time']=$shop['shop_time'];
        }
        //主营类目
        $shop['cate_name']=Db::name('shop_category')->where('id',$shop['cid'])->value('name');
        // 店铺推荐商品
        $shop['goods_list'] = Goods::field('id,image,name,min_price,market_price')
            ->where($onSaleWhere)
            ->where([
                'shop_id' => $shop['id'],
                'is_recommend' => 1, // 推荐商品
            ])
            ->limit(9)
            ->select()
            ->toArray();

        // 用户是否关注店铺
        $shop['shop_follow_status'] = 0;
        $shop['score'] = max($shop['score'], 3);
        $shop['star'] = max($shop['star'], 3);
        if($userId) { // 用户已登录
            $shopFollow = ShopFollow::where(['user_id'=>$userId, 'shop_id'=>$shopId])->findOrEmpty();
            if(!$shopFollow->isEmpty()) {
                $shop['shop_follow_status'] = $shopFollow['status'];
            }
            $shopfootprint = Db::name('shop_footprint')->where(['user_id'=>$userId, 'shop_id'=>$shopId])->value('id');
            if(!empty($shopfootprint)) {
                //nums递增1
                Db::name('shop_footprint')->where(['id'=>$shopfootprint])->inc('nums',1)->update(['update_time' => time()]);
            }else{

                Db::name('shop_footprint')->insert(['user_id'=>$userId,'shop_id'=>$shopId, 'create_time'=>time(),'nums'=>1]);
            }

        }
        $cha=time() -(int)$shop['update_time'];
        //一天更新一次店铺的回头率
        if($cha > 86400){
            $shop_model=new Shop();
            $is_return=$shop_model->getReturnRate($shopId);
            Shop::where('id', $shopId)->update(['return_rate' => round($is_return,2),'update_time'=>time()]);
        }



        $shop['follow_num']         = ShopFollow::where(['shop_id' => $shopId,'status' => 1])->count('id');
        $image                      = ConfigServer::get('shop_customer_service', 'image', '', $shopId);
        $shop['customer_image']     = $image ? UrlServer::getFileUrl($image) : '';
        $shop['customer_wechat']    = ConfigServer::get('shop_customer_service', 'wechat', '', $shopId);
        $shop['customer_phone']     = ConfigServer::get('shop_customer_service', 'phone', '', $shopId);
        
        // 店铺广告
        $adWhere = [
            [ 'shop_id', '=', $shopId ],
            [ 'status', '=', 1 ],
        ];
        $shop['ad'] = [
            'pc'        => ShopAd::where($adWhere)->where('terminal', ShopAdEnum::TERMINAL_PC)->append([ 'link_path', 'link_query' ])->order('sort desc,id desc')->select()->toArray(),
            'mobile'    => ShopAd::where($adWhere)->where('terminal', ShopAdEnum::TERMINAL_MOBILE)->append([ 'link_path', 'link_query' ])->order('sort desc,id desc')->select()->toArray(),
        ];
        
        return $shop;
    }


    /*
     * 找工厂-年度榜
     */
    public static function getManufacturerAnnualTopList($get){
        //当前年的开始到结束时间
        $start_time = strtotime(date('Y-01-01'));
        $end_time = time();
        $where_order[]=['o.create_time','between',[$start_time,$end_time]];
        //获取订单id
        $order_ids=OrderGoods::where($where_order)->alias('o')
            ->field('s.shop_id,count(o.goods_num) as num')
            ->LeftJoin('goods s','s.id=o.goods_id')
            ->where([
                ['o.refund_status','=',0],
                ['s.del','=',0],
                ['s.status','=',1],
            ])
            ->group('s.shop_id')
            ->order('num desc')
            ->select()->toArray();
        //更新goods表中的year_sales字段
        foreach ($order_ids as $item){
            $shops=Shop::find($item['shop_id']);
            if(!empty($shops)){
                $shops->year_sales=$item['num'];
//                $shops->save();
            }
        }
        $get['order']=[
            'year_sales'=>'desc',
            'return_rate'=>'desc',
            'score'=>'desc'
        ];

        return self::getShopList($get);
    }


    /*
     * 找工厂-收藏榜
     */
    public static function getManufacturerFavorites($get){

        $shops=Db::name('shop_follow')
            ->field('shop_id,count(id) as num')
            ->where('status',1)
            ->group('shop_id')
            ->order('num desc')
            ->select()->toArray();
        if(!empty($shops)){
            foreach ($shops as $item){
                $shops_id=$item['shop_id'];
                Shop::where('id',$shops_id)->update(['follow_nums'=>$item['num']]);
            }
        }
        $get['order']=[
            'follow_nums'=>'desc',
            'visited_num'=>'desc',
            'return_rate'=>'desc',

        ];

        return self::getShopList($get);
    }
    /*
    * 找工厂-热销企业
    */
    public static function getHotManufacturers($get){
        //当前年的开始到结束时间

        //获取订单id
        $order_ids=OrderGoods::alias('o')
            ->field('s.shop_id,count(o.goods_num) as num')
            ->LeftJoin('goods s','s.id=o.goods_id')
            ->where([
                ['o.refund_status','=',0],
                ['s.del','=',0],
                ['s.status','=',1],
            ])
            ->group('s.shop_id')
            ->order('num desc')
            ->select()->toArray();
        //更新goods表中的total_sales字段
        foreach ($order_ids as $item){
            $shops=Shop::find($item['shop_id']);
            if(!empty($shops)){
                $shops->total_sales=$item['num'];
                $shops->save();
            }
        }

        $get['order']=[
            'total_sales'=>'desc',
            'return_rate'=>'desc',
        ];
        return self::getShopList($get);
    }

    /*
     * 找工厂-权威验厂
     */
    public static function getverifyAuthorizedFactory($get){

        $get['yan_levels']=1;
        $get['order']=[
            'yan_level'=>'desc',
            'visited_num'=>'desc',
            'return_rate'=>'desc',
        ];
        return self::getShopList($get);
    }


     /*
     * 找工厂-集采联盟
     */
    public static function getexecuteCollectiveProcurement($get){

        $get['jcshop_vip']=1;

        return self::getShopList($get);
    }



    /**
     * 店铺列表
     */
    public static function getShopList($get)
    {
        $where = [
            ['is_freeze', '=', 0], // 未冻结
            ['del', '=', 0], // 未删除
            ['is_run', '=', 1], // 未暂停营业
        ];
        $shop_ids=[];
        // 处理关键词搜索（支持 name 和 keyword 参数）
        $searchKeyword = '';
        if(isset($get['keyword']) && !empty($get['keyword'])) {
            $searchKeyword = trim($get['keyword']);
        } elseif(isset($get['name']) && !empty($get['name'])) {
            $searchKeyword = trim($get['name']);
        }

        if (!empty($searchKeyword)) {
            // 使用优化的搜索逻辑查找相关商品的店铺
            $shop_ids = self::searchShopsByGoodsKeyword($searchKeyword);
        }
        // 权威验厂
        if(isset($get['yan_level']) && !empty($get['yan_level'])) {
            $where[] = ['yan_level', '=',$get['yan_level']];
        }else{
            if(isset($get['yan_levels']) && !empty($get['yan_levels'])){
                $where[] = ['yan_level', '>',0];
            }
        }
          if(isset($get['is_recommend']) && !empty($get['is_recommend'])) {
            $where[] = ['is_recommend', '=',$get['is_recommend']];
        }
        if(isset($get['jcshop_vip']) && !empty($get['jcshop_vip'])) {
            $where[] = ['jcshop_vip', '=',$get['jcshop_vip']];
        }


        // 主营类目
        if(isset($get['shop_cate_id']) && !empty($get['shop_cate_id'])) {
            $where[] = ['cid', '=', $get['shop_cate_id']];
        }

        //一级分类-找厂家
        if(isset($get['first_cate_id']) && !empty($get['first_cate_id'])){
           $shop_id= Db::name('goods')->where(['first_cate_id'=>$get['first_cate_id'],'audit_status'=>1,'status'=>1])->group('shop_id')->column('shop_id');
           if($shop_id){
               $where[] = ['id', 'in', $shop_id];
           }else{
               $data = [
                   'lists' => [],
                   'count' => 0,
                   'more' =>0,
                   'page_no' => $get['page_no'],
                   'page_isze' => $get['page_size']
               ];
               return $data;
           }

           $where[] = ['id', 'in', $shop_id];
        }
        //二级分类-找厂家
        if(isset($get['second_cate_id']) && !empty($get['second_cate_id'])){
            $shop_id= Db::name('goods')->where(['second_cate_id'=>$get['second_cate_id'],'audit_status'=>1,'status'=>1])->group('shop_id')->column('shop_id');
            if($shop_id){
                $where[] = ['id', 'in', $shop_id];
            }else{
                $data = [
                    'lists' => [],
                    'count' => 0,
                    'more' =>0,
                    'page_no' => $get['page_no'],
                    'page_isze' => $get['page_size']
                ];
                return $data;
            }

            $where[] = ['id', 'in', $shop_id];
        }

        //三级分类-找厂家
        if(isset($get['third_cate_id']) && !empty($get['third_cate_id'])){
            $shop_id= Db::name('goods')->where(['third_cate_id'=>$get['third_cate_id'],'audit_status'=>1,'status'=>1])->group('shop_id')->column('shop_id');
            if($shop_id){
                $where[] = ['id', 'in', $shop_id];
            }else{
                $data = [
                    'lists' => [],
                    'count' => 0,
                    'more' =>0,
                    'page_no' => $get['page_no'],
                    'page_isze' => $get['page_size']
                ];
                return $data;
            }

            $where[] = ['id', 'in', $shop_id];
        }

        if(isset($get['type']) && !empty($get['type'])) {
            $text_type=[1=>'return_rate',2=>'visited_num',3=>'yan_level'];
            $order = [
                $text_type[$get['type']] => 'desc'
            ];
        }else{
            if(isset($get['order']) && !empty($get['order'])){
                 $order = $get['order'];
            }else{
                $order = [
                    'yan_level'=>'desc',
                    'weight' => 'asc',
                    'score' => 'desc',
                    'id' => 'desc'
                ];
            }
        }


        $list = Shop::field(true)
            ->where($where)
            ->where(function ($query) {
                $query->where(function($q) {
                    $q->where('business_license', '<>', '')
                      ->whereNotNull('business_license');
                })->where(function($q) {
                    $q->whereIn('id', function($subQuery) {
                        $subQuery->table('ls_goods')
                                 ->where('audit_status', 1)
                                 ->group('shop_id')
                                 ->having('count(id) > 1')
                                 ->field('shop_id');
                    });
                });
            })
            ->where(function ($query) use ($get,$shop_ids) {
                // 处理关键词搜索（支持 name 和 keyword 参数）
                $searchKeyword = '';
                if(isset($get['keyword']) && !empty($get['keyword'])) {
                    $searchKeyword = trim($get['keyword']);
                } elseif(isset($get['name']) && !empty($get['name'])) {
                    $searchKeyword = trim($get['name']);
                }

                if (!empty($searchKeyword)) {
                    if(isset($shop_ids) && !empty($shop_ids)){
                        $query->whereLike('name', '%' . $searchKeyword . '%')->whereOr(['id'=>$shop_ids]);
                    }else{
                        $query->whereLike('name', '%' . $searchKeyword . '%');
                    }
                }
            })
            // 无限期 或 未到期
            ->whereRaw('expire_time =0 OR expire_time > '. time())
            ->order($order)
            ->page($get['page_no'], $get['page_size'])
            ->select()
            ->toArray();

        $count = Shop::where($where)
            // 无限期 或 未到期
            ->whereRaw('expire_time =0 OR expire_time > '. time())
            ->count();

        $onSaleWhere = [
            ['del', '=', GoodsEnum::DEL_NORMAL],  // 未删除
            ['status', '=', GoodsEnum::STATUS_SHELVES], // 上架中
            ['audit_status', '=', GoodsEnum::AUDIT_STATUS_OK], // 审核通过
        ];
      
        foreach($list as &$shop) {
            $shop['goods_list'] = Goods::field('id,image,name,min_price,market_price')
                ->where($onSaleWhere)
                ->where([
                    'shop_id' => $shop['id'],
                ])
                ->limit(10)
                ->select()
                ->toArray();
            $shop['on_sale_goods'] = count($shop['goods_list']);
            // logo及背景图
            $shop['logo']       = $shop['logo'] ? UrlServer::getFileUrl($shop['logo']) : UrlServer::getFileUrl(ShopEnum::DEFAULT_LOGO);
            $shop['background'] = $shop['background'] ? UrlServer::getFileUrl($shop['background']) : UrlServer::getFileUrl(ShopEnum::DEFAULT_BG);
            // $shop['cover']      = $shop['cover'] ? UrlServer::getFileUrl($shop['cover']) : UrlServer::getFileUrl(ShopEnum::DEFAULT_COVER);
            $shop['cover']=  $shop['logo'];
            $shop['banner']     = $shop['banner'] ? UrlServer::getFileUrl($shop['banner']) : '';
            $jc_vip='/uploads/images/20250109/202501091712370e7414006.png';
            $tui='/uploads/images/20250109/20250109172215b02214651.png';
            $shop['jcshop_vip_image']=$shop['jcshop_vip']?UrlServer::getFileUrl($jc_vip):'';
            $shop['tui_image']=$shop['is_recommend']==1?UrlServer::getFileUrl($tui):'';
            // 获取商店等级信息，添加空值检查
            $shopLevel = null;
            if (!empty($shop['tier_level'])) {
                $shopLevel = Db::name('shop_tier_config')->where('tier_level', $shop['tier_level'])->find();
            }

            // 设置等级图片和名称，确保安全访问
            $shop['yan_level_image'] =$shopLevel['tier_image']??'';
            $shop['yan_level'] =$shopLevel['tier_name']??'';
            $shop['shop_label']=  $shop['shop_label']?explode(',',$shop['shop_label']):[];
            //根据创建时间,判断入驻多久了,按年算,不足一年按年算

            $shop['shop_time'] =(int)date('Y',time())-(int)date('Y',strtotime($shop['create_time']));
            if($shop['shop_time']<1){
                $shop['shop_time']=1;
            }else{
                $shop['shop_time']=$shop['shop_time'];
            }
            $shop['score']=$shop['score']?$shop['score']:3;
            //主营类目
            $shop['cate_name']=Db::name('shop_category')->where('id',$shop['cid'])->value('name');
            // 店铺地址
            $shop['province']    = DevRegion::getAreaName($shop['province_id']);
            $shop['city']        = DevRegion::getAreaName($shop['city_id']);
            $shop['district']    = DevRegion::getAreaName($shop['district_id']);

        }

        $more = is_more($count, $get['page_no'], $get['page_size']);

        $data = [
            'lists' => $list,
            'count' => $count,
            'more' => $more,
            'page_no' => $get['page_no'],
            'page_isze' => $get['page_size']
        ];

        return $data;
    }


    /**
     * @notes 附近店铺列表
     * @param $get
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date 2022/9/20 4:29 下午
     */
    public static function getNearbyShops($get)
    {
        $where = [
            ['is_freeze', '=', 0], // 未冻结
            ['del', '=', 0], // 未删除
            ['is_run', '=', 1], // 未暂停营业
            ['city_id', '=', $get['city_id']],
        ];

        // 店铺名称
        if(isset($get['name']) && !empty($get['name'])) {
            $where[] = ['name', 'like', '%'. trim($get['name']. '%')];
        }

        // 主营类目
        if(isset($get['shop_cate_id']) && !empty($get['shop_cate_id'])) {
            $where[] = ['cid', '=', $get['shop_cate_id']];
        }

        $city = DevRegion::where('id',$get['city_id'])->field('db09_lng,db09_lat')->findOrEmpty()->toArray();

        $list = Shop::field('id,name,logo,background,visited_num,cover,banner,st_distance_sphere(point('.$city['db09_lng'].','.$city['db09_lat'].'),point(longitude, latitude)) as distance')
            ->where($where)
            // 无限期 或 未到期
            ->whereRaw('expire_time =0 OR expire_time > '. time())
            ->order('distance asc')
            ->page($get['page_no'], $get['page_size'])
            ->select()
            ->toArray();

        $count = Shop::where($where)
            // 无限期 或 未到期
            ->whereRaw('expire_time =0 OR expire_time > '. time())
            ->count();

        $onSaleWhere = [
            ['del', '=', GoodsEnum::DEL_NORMAL],  // 未删除
            ['status', '=', GoodsEnum::STATUS_SHELVES], // 上架中
            ['audit_status', '=', GoodsEnum::AUDIT_STATUS_OK], // 审核通过
        ];
        foreach($list as &$shop) {
            $shop['goods_list'] = Goods::field('id,image,name,min_price,market_price')
                ->where($onSaleWhere)
                ->where([
                    'shop_id' => $shop['id'],
                ])
                ->select()
                ->toArray();
            $shop['on_sale_goods'] = count($shop['goods_list']);
            // logo及背景图
            $shop['logo'] = $shop['logo'] ? UrlServer::getFileUrl($shop['logo']) : UrlServer::getFileUrl(ShopEnum::DEFAULT_LOGO);
            $shop['background'] = $shop['background'] ? UrlServer::getFileUrl($shop['background']) : UrlServer::getFileUrl(ShopEnum::DEFAULT_BG);
            $shop['cover'] = $shop['cover'] ? UrlServer::getFileUrl($shop['cover']) : UrlServer::getFileUrl(ShopEnum::DEFAULT_COVER);
            $shop['banner'] = $shop['banner'] ? UrlServer::getFileUrl($shop['banner']) : '';

            //转换距离单位
            if ($shop['distance'] < 1000) {
                $shop['distance'] = round($shop['distance']).'m';
            }else {
                $shop['distance'] = round($shop['distance'] / 1000,2).'km';
            }
        }

        $more = is_more($count, $get['page_no'], $get['page_size']);

        $data = [
            'list' => $list,
            'count' => $count,
            'more' => $more,
            'page_no' => $get['page_no'],
            'page_isze' => $get['page_size']
        ];

        return $data;
    }

    /**
     * 根据商品关键词搜索相关店铺ID
     *
     * @param string $keyword 搜索关键词
     * @return array 店铺ID数组
     */
    protected static function searchShopsByGoodsKeyword($keyword)
    {
        // 基础搜索条件
        $goodsWhere = [
            ['del', '=', 0],
            ['status', '=', 1],
            ['audit_status', '=', 1]
        ];

        // 获取同义词
        $synonyms = self::getSynonyms($keyword);

        // 构建搜索条件
        $shopIds = [];

        // 1. 精确匹配原关键词
        $exactMatchIds = \think\facade\Db::name('goods')
            ->where($goodsWhere)
            ->where(function($query) use ($keyword) {
                $query->whereLike('name', '%' . $keyword . '%')
                      ->whereOr('remark', 'like', '%' . $keyword . '%')
                      ->whereOr('content', 'like', '%' . $keyword . '%');
            })
            ->group('shop_id')
            ->column('shop_id');

        if (!empty($exactMatchIds)) {
            $shopIds = array_merge($shopIds, $exactMatchIds);
        }

        // 2. 同义词匹配
        if (!empty($synonyms)) {
            foreach ($synonyms as $synonym) {
                $synonymIds = \think\facade\Db::name('goods')
                    ->where($goodsWhere)
                    ->where(function($query) use ($synonym) {
                        $query->whereLike('name', '%' . $synonym . '%')
                              ->whereOr('remark', 'like', '%' . $synonym . '%')
                              ->whereOr('content', 'like', '%' . $synonym . '%');
                    })
                    ->group('shop_id')
                    ->column('shop_id');

                if (!empty($synonymIds)) {
                    $shopIds = array_merge($shopIds, $synonymIds);
                }
            }
        }

        // 去重并返回
        return array_unique($shopIds);
    }

    /**
     * 获取关键词的同义词和相关词
     *
     * @param string $keyword 原始关键词
     * @return array 同义词数组
     */
    protected static function getSynonyms($keyword)
    {
        // 定义同义词映射表
        $synonymMap = [
            // 眼镜相关
            '眼镜' => ['老花镜', '近视镜', '太阳镜', '墨镜', '护目镜'],
            '老花镜' => ['眼镜', '花镜', '老花眼镜', '老人眼镜'],
            '近视镜' => ['眼镜', '近视眼镜'],
            '太阳镜' => ['眼镜', '墨镜', '遮阳镜'],
            '墨镜' => ['眼镜', '太阳镜', '遮阳镜'],

            // 鞋类相关
            '老人鞋' => ['健步鞋', '中老年鞋', '老年鞋', '舒适鞋'],
            '健步鞋' => ['老人鞋', '运动鞋', '休闲鞋'],
            '运动鞋' => ['跑步鞋', '健步鞋', '休闲鞋'],

            // 医疗器械相关
            '轮椅' => ['代步车', '助行器', '行走辅助器'],
            '拐杖' => ['助行器', '手杖', '行走辅助器'],
            '助行器' => ['拐杖', '轮椅', '行走辅助器'],

            // 护理用品相关
            '护理垫' => ['尿垫', '防漏垫', '床垫'],
            '成人纸尿裤' => ['尿不湿', '纸尿片', '护理垫'],
        ];

        $keyword = trim($keyword);
        $synonyms = [];

        // 直接匹配
        if (isset($synonymMap[$keyword])) {
            $synonyms = $synonymMap[$keyword];
        }

        // 模糊匹配（包含关系）
        foreach ($synonymMap as $key => $values) {
            if (strpos($keyword, $key) !== false || strpos($key, $keyword) !== false) {
                $synonyms = array_merge($synonyms, $values);
            }
        }

        // 去重并过滤掉原关键词
        $synonyms = array_unique($synonyms);
        $synonyms = array_filter($synonyms, function($synonym) use ($keyword) {
            return $synonym !== $keyword;
        });

        return array_values($synonyms);
    }
}
