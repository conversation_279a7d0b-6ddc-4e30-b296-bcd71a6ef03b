-- 初始化采购套餐测试数据

-- 清空现有数据
TRUNCATE TABLE `ls_purchaser_package`;
TRUNCATE TABLE `ls_purchaser`;

-- 插入采购套餐数据
INSERT INTO `ls_purchaser_package` (`name`, `purchaser_count`, `price`, `status`, `sort`, `create_time`, `update_time`) VALUES
('基础套餐', 2, 299.00, 1, 100, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('标准套餐', 5, 699.00, 1, 200, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('高级套餐', 10, 1299.00, 1, 300, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('企业套餐', 20, 2399.00, 1, 400, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- 插入采购人员数据
INSERT INTO `ls_purchaser` (`name`, `mobile`, `email`, `level`, `max_shops`, `assigned_shops`, `status`, `create_time`, `update_time`) VALUES
-- 初级采购人员
('张小采', '13800138001', '<EMAIL>', 1, 15, 0, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('李小采', '13800138002', '<EMAIL>', 1, 15, 0, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('王小采', '13800138003', '<EMAIL>', 1, 15, 0, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('赵小采', '13800138004', '<EMAIL>', 1, 15, 0, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('刘小采', '13800138005', '<EMAIL>', 1, 15, 0, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('陈小采', '13800138006', '<EMAIL>', 1, 15, 0, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('杨小采', '13800138007', '<EMAIL>', 1, 15, 0, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('黄小采', '13800138008', '<EMAIL>', 1, 15, 0, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),

-- 中级采购人员
('张中采', '13800138011', '<EMAIL>', 2, 12, 0, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('李中采', '13800138012', '<EMAIL>', 2, 12, 0, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('王中采', '13800138013', '<EMAIL>', 2, 12, 0, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('赵中采', '13800138014', '<EMAIL>', 2, 12, 0, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('刘中采', '13800138015', '<EMAIL>', 2, 12, 0, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('陈中采', '13800138016', '<EMAIL>', 2, 12, 0, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),

-- 高级采购人员
('张高采', '13800138021', '<EMAIL>', 3, 8, 0, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('李高采', '13800138022', '<EMAIL>', 3, 8, 0, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('王高采', '13800138023', '<EMAIL>', 3, 8, 0, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('赵高采', '13800138024', '<EMAIL>', 3, 8, 0, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- 查看插入结果
SELECT '采购套餐数据:' as info;
SELECT * FROM `ls_purchaser_package`;

SELECT '采购人员数据:' as info;
SELECT `name`, `level`, `max_shops`, `assigned_shops`, `status` FROM `ls_purchaser` ORDER BY `level`, `id`;

-- 统计信息
SELECT '统计信息:' as info;
SELECT 
    '套餐总数' as item, 
    COUNT(*) as count 
FROM `ls_purchaser_package` 
WHERE `status` = 1

UNION ALL

SELECT 
    CONCAT('等级', level, '采购人员') as item,
    COUNT(*) as count
FROM `ls_purchaser` 
WHERE `status` = 1
GROUP BY `level`
ORDER BY item;
