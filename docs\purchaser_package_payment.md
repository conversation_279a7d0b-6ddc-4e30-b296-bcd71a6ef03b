# 采购套餐购买支付系统

## 概述

本系统实现了采购套餐的购买、支付和采购人员自动分配功能。商家可以根据自己的等级购买不同的采购套餐，系统会自动分配相应等级的采购人员。

## 功能特性

1. **套餐管理**: 支持多种采购套餐，包含不同的人数和价格
2. **支付集成**: 支持微信支付和支付宝支付
3. **自动分配**: 根据商家等级自动分配相应等级的采购人员
4. **回调处理**: 完整的支付回调处理和分配逻辑
5. **订单管理**: 完整的订单创建、查询、取消功能

## 数据表结构

### 1. 采购套餐表 (ls_purchaser_package)
```sql
CREATE TABLE `ls_purchaser_package` (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL COMMENT '套餐名称',
  `purchaser_count` int(11) NOT NULL DEFAULT 0 COMMENT '分配人数',
  `price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '套餐价格',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态(0=禁用, 1=启用)',
  `sort` int(11) NOT NULL DEFAULT 100 COMMENT '排序',
  `create_time` int(11) NULL DEFAULT NULL,
  `update_time` int(11) NULL DEFAULT NULL,
  `delete_time` int(11) NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
);
```

### 2. 采购套餐订单表 (ls_purchaser_package_order)
```sql
CREATE TABLE `ls_purchaser_package_order` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `order_sn` varchar(32) NOT NULL COMMENT '订单号',
  `shop_id` int(11) NOT NULL COMMENT '购买的商家ID',
  `package_id` int(11) NOT NULL COMMENT '购买的套餐ID',
  `package_name` varchar(255) NOT NULL COMMENT '套餐名称',
  `purchaser_count` int(11) NOT NULL COMMENT '购买的分配人数',
  `price` decimal(10,2) NOT NULL COMMENT '支付价格',
  `pay_status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '支付状态(0=未支付, 1=已支付)',
  `pay_time` int(11) DEFAULT NULL COMMENT '支付时间',
  `transaction_id` varchar(64) DEFAULT NULL COMMENT '第三方支付流水号',
  `create_time` int(11) DEFAULT NULL,
  `update_time` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
);
```

### 3. 采购人员表 (ls_purchaser)
```sql
CREATE TABLE `ls_purchaser` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '采购人员姓名',
  `mobile` varchar(20) NOT NULL COMMENT '手机号',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `level` tinyint(1) NOT NULL DEFAULT '1' COMMENT '采购等级(1=初级, 2=中级, 3=高级)',
  `max_shops` int(11) DEFAULT NULL COMMENT '最大可分配商家数量(NULL=无限制)',
  `assigned_shops` int(11) NOT NULL DEFAULT '0' COMMENT '已分配商家数量',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态(0=禁用, 1=启用)',
  `create_time` int(11) DEFAULT NULL,
  `update_time` int(11) DEFAULT NULL,
  `delete_time` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
);
```

### 4. 采购人员分配记录表 (ls_purchaser_allocation)
```sql
CREATE TABLE `ls_purchaser_allocation` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `shop_id` int(11) NOT NULL COMMENT '商家ID',
  `purchaser_id` int(11) NOT NULL COMMENT '采购人员ID',
  `package_order_id` int(11) NOT NULL COMMENT '套餐订单ID',
  `allocated_time` int(11) NOT NULL COMMENT '分配时间',
  `expire_time` int(11) NOT NULL COMMENT '到期时间',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态(0=失效, 1=有效)',
  `create_time` int(11) DEFAULT NULL,
  `update_time` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
);
```

## API接口

### 1. 获取套餐列表
```
GET /shopapi/purchaser_package/packageList
```

### 2. 创建订单
```
POST /shopapi/purchaser_package/createOrder
{
    "package_id": 1
}
```

### 3. 支付订单
```
POST /shopapi/purchaser_package/payOrder
{
    "order_sn": "PP20231201123456",
    "pay_way": 1
}
```

### 4. 获取订单列表
```
GET /shopapi/purchaser_package/orderList?page_no=1&page_size=10
```

### 5. 获取已分配采购人员
```
GET /shopapi/purchaser_package/allocatedPurchasers
```

## 分配规则

根据商家等级分配不同等级的采购人员：

- **0元入驻商家 (level=0)**: 只能分配初级采购人员 (level=1)
- **商家会员 (level=1)**: 可分配初级、中级采购人员 (level=1,2)
- **实力厂商 (level=2)**: 可分配所有等级采购人员 (level=1,2,3)

## 支付流程

1. 商家选择套餐并创建订单
2. 调用支付接口发起支付
3. 用户完成支付
4. 支付平台回调通知
5. 系统处理回调，更新订单状态
6. 自动分配采购人员
7. 记录分配结果

## 安装部署

1. 执行数据库脚本：
```bash
# 创建采购套餐相关表
mysql -u username -p database_name < database/purchaser_package.sql
mysql -u username -p database_name < database/purchaser_allocation.sql
```

2. 配置支付参数（微信支付、支付宝支付）

3. 初始化采购人员数据

4. 配置定时任务处理过期分配

## 测试

访问 `/test_purchaser_package.html` 进行功能测试。

注意：测试前需要：
1. 配置正确的API认证token
2. 确保数据库中有套餐数据
3. 确保有可用的采购人员数据

## 注意事项

1. 支付回调URL需要配置为公网可访问地址
2. 采购人员分配有效期默认为1年
3. 系统会自动检查采购人员的分配数量限制
4. 建议定期清理过期的分配记录
5. 支付金额单位为元，传递给支付平台时会转换为分

## 扩展功能

1. 可以添加套餐有效期限制
2. 可以添加采购人员工作量统计
3. 可以添加分配历史查询
4. 可以添加自动续费功能
5. 可以添加采购人员评价系统
