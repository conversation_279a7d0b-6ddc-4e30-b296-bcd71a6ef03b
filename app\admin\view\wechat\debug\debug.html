{layout name="layout1" /}
<div class="wrapper">
    <div class="layui-card">
        <div class="layui-card-header">
            <h3>微信进采购群功能调试工具</h3>
        </div>
        <div class="layui-card-body">
            
            <!-- 配置检查 -->
            <div class="layui-collapse">
                <div class="layui-colla-item">
                    <h2 class="layui-colla-title">1. 配置检查</h2>
                    <div class="layui-colla-content">
                        <button class="layui-btn" onclick="checkConfig()">检查配置</button>
                        <div id="config-result" style="margin-top: 15px;"></div>
                    </div>
                </div>
            </div>
            
            <!-- 图片上传测试 -->
            <div class="layui-collapse">
                <div class="layui-colla-item">
                    <h2 class="layui-colla-title">2. 图片上传测试</h2>
                    <div class="layui-colla-content">
                        <p>测试将二维码图片上传到微信服务器获取media_id</p>
                        <button class="layui-btn" onclick="testUpload()">测试上传</button>
                        <div id="upload-result" style="margin-top: 15px;"></div>
                    </div>
                </div>
            </div>
            
            <!-- 消息发送测试 -->
            <div class="layui-collapse">
                <div class="layui-colla-item">
                    <h2 class="layui-colla-title">3. 消息发送测试</h2>
                    <div class="layui-colla-content">
                        <div class="layui-form-item">
                            <label class="layui-form-label">测试用户OpenID：</label>
                            <div class="layui-input-inline">
                                <input type="text" id="test-openid" placeholder="请输入测试用户的OpenID" class="layui-input">
                            </div>
                            <button class="layui-btn" onclick="testSend()">发送测试消息</button>
                        </div>
                        <div class="layui-form-mid layui-word-aux">
                            注意：请使用真实的用户OpenID进行测试
                        </div>
                        <div id="send-result" style="margin-top: 15px;"></div>
                    </div>
                </div>
            </div>
            
            <!-- 日志查看 -->
            <div class="layui-collapse">
                <div class="layui-colla-item">
                    <h2 class="layui-colla-title">4. 日志查看</h2>
                    <div class="layui-colla-content">
                        <p>请查看以下日志文件获取详细信息：</p>
                        <ul>
                            <li>微信日志：runtime/wechat/当前年月/当前日期.log</li>
                            <li>应用日志：runtime/log/当前日期.log</li>
                        </ul>
                    </div>
                </div>
            </div>
            
        </div>
    </div>
</div>

<style>
.result-box {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 15px;
    margin-top: 10px;
}
.success {
    border-color: #28a745;
    background-color: #d4edda;
    color: #155724;
}
.error {
    border-color: #dc3545;
    background-color: #f8d7da;
    color: #721c24;
}
.config-item {
    margin-bottom: 10px;
    padding: 8px;
    background: #fff;
    border-left: 3px solid #007cba;
}
.status-ok {
    color: #28a745;
    font-weight: bold;
}
.status-error {
    color: #dc3545;
    font-weight: bold;
}
</style>

<script>
layui.config({
    version:"{$front_version}",
    base: '/static/lib/'
}).use(['layer'], function(){
    var layer = layui.layer;
    
    window.checkConfig = function() {
        like.ajax({
            url: '{:url("wechat.debug/index")}',
            data: {action: 'check_config'},
            type: 'post',
            success: function(res) {
                if (res.code == 1) {
                    showConfigResult(res.data);
                } else {
                    showResult('config-result', res.msg, false);
                }
            }
        });
    };
    
    window.testUpload = function() {
        showResult('upload-result', '正在测试上传...', true);
        
        like.ajax({
            url: '{:url("wechat.debug/index")}',
            data: {action: 'test_upload'},
            type: 'post',
            success: function(res) {
                if (res.code == 1) {
                    var html = '<div class="result-box success">';
                    html += '<h4>上传成功</h4>';
                    html += '<p><strong>Media ID:</strong> ' + res.data.result.media_id + '</p>';
                    html += '<p><strong>文件路径:</strong> ' + res.data.image_path + '</p>';
                    html += '<p><strong>文件大小:</strong> ' + res.data.file_size + ' bytes</p>';
                    html += '<p><strong>创建时间:</strong> ' + new Date(res.data.result.created_at * 1000).toLocaleString() + '</p>';
                    html += '</div>';
                    document.getElementById('upload-result').innerHTML = html;
                } else {
                    showResult('upload-result', res.msg, false);
                }
            }
        });
    };
    
    window.testSend = function() {
        var openid = document.getElementById('test-openid').value;
        if (!openid) {
            layer.msg('请输入OpenID');
            return;
        }
        
        showResult('send-result', '正在发送测试消息...', true);
        
        like.ajax({
            url: '{:url("wechat.debug/index")}',
            data: {action: 'test_send', openid: openid},
            type: 'post',
            success: function(res) {
                if (res.code == 1) {
                    var html = '<div class="result-box success">';
                    html += '<h4>发送完成</h4>';
                    if (res.data.text) {
                        html += '<p><strong>文本消息:</strong> ' + JSON.stringify(res.data.text) + '</p>';
                    }
                    if (res.data.upload) {
                        html += '<p><strong>图片上传:</strong> ' + JSON.stringify(res.data.upload) + '</p>';
                    }
                    if (res.data.image) {
                        html += '<p><strong>图片消息:</strong> ' + JSON.stringify(res.data.image) + '</p>';
                    }
                    if (res.data.error) {
                        html += '<p><strong>错误:</strong> ' + res.data.error + '</p>';
                    }
                    html += '</div>';
                    document.getElementById('send-result').innerHTML = html;
                } else {
                    showResult('send-result', res.msg, false);
                }
            }
        });
    };
    
    function showConfigResult(data) {
        var html = '<div class="result-box success">';
        html += '<h4>配置检查结果</h4>';
        
        // 微信配置
        html += '<div class="config-item">';
        html += '<h5>微信配置</h5>';
        html += '<p>APP ID: ' + data.wechat_config.app_id + '</p>';
        html += '<p>Secret: ' + data.wechat_config.secret + '</p>';
        html += '<p>Token: ' + data.wechat_config.token + '</p>';
        html += '<p>Access Token: ' + (data.wechat_config.access_token ? '已获取' : '获取失败') + '</p>';
        html += '</div>';
        
        // 回复配置
        if (data.reply_config) {
            html += '<div class="config-item">';
            html += '<h5>回复配置</h5>';
            html += '<p>关键词: ' + data.reply_config.keyword + '</p>';
            html += '<p>内容类型: ' + data.reply_config.content_type + '</p>';
            html += '<p>消息数量: ' + data.reply_config.message_count + '</p>';
            html += '<p>第二条消息类型: ' + data.reply_config.second_content_type + '</p>';
            html += '<p>状态: ' + (data.reply_config.status == 1 ? '启用' : '停用') + '</p>';
            html += '</div>';
        }
        
        // 图片检查
        if (data.image_check) {
            html += '<div class="config-item">';
            html += '<h5>图片检查</h5>';
            html += '<p>路径: ' + data.image_check.path + '</p>';
            html += '<p>存在: ' + (data.image_check.exists ? '是' : '否') + '</p>';
            html += '<p>大小: ' + data.image_check.size + ' bytes</p>';
            html += '</div>';
        }
        
        html += '</div>';
        document.getElementById('config-result').innerHTML = html;
    }
    
    function showResult(elementId, message, isSuccess) {
        var className = isSuccess ? 'success' : 'error';
        var html = '<div class="result-box ' + className + '">' + message + '</div>';
        document.getElementById(elementId).innerHTML = html;
    }
});
</script>
