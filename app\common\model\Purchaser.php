<?php
namespace app\common\model;

use app\common\basics\Models;
use think\model\concern\SoftDelete;

/**
 * 采购人员模型
 * Class Purchaser
 * @package app\common\model
 */
class Purchaser extends Models
{
    use SoftDelete;
    protected $deleteTime = 'delete_time';
    protected $defaultSoftDelete = 0;
    
    /**
     * 获取采购等级文本
     * @param $value
     * @param $data
     * @return string
     */
    public function getLevelTextAttr($value, $data)
    {
        $levels = [
            1 => '初级采购',
            2 => '中级采购', 
            3 => '高级采购'
        ];
        
        return $levels[$data['level']] ?? '未知等级';
    }
    
    /**
     * 获取状态文本
     * @param $value
     * @param $data
     * @return string
     */
    public function getStatusTextAttr($value, $data)
    {
        return $data['status'] == 1 ? '启用' : '禁用';
    }
}
