<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>微信回复功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 5px;
        }
        .test-section h3 {
            color: #007cba;
            margin-top: 0;
        }
        .test-item {
            margin-bottom: 15px;
            padding: 10px;
            background-color: #f9f9f9;
            border-left: 4px solid #007cba;
        }
        .status {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 3px;
            font-size: 12px;
            font-weight: bold;
        }
        .status.pending {
            background-color: #ffc107;
            color: #856404;
        }
        .status.success {
            background-color: #28a745;
            color: white;
        }
        .status.error {
            background-color: #dc3545;
            color: white;
        }
        .test-button {
            background-color: #007cba;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        .test-button:hover {
            background-color: #005a87;
        }
        .result-area {
            margin-top: 15px;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 4px;
            min-height: 50px;
        }
        .checklist {
            list-style-type: none;
            padding: 0;
        }
        .checklist li {
            margin-bottom: 10px;
            padding: 8px;
            background-color: #f8f9fa;
            border-radius: 4px;
        }
        .checklist li:before {
            content: "☐ ";
            font-size: 16px;
            margin-right: 8px;
        }
        .checklist li.checked:before {
            content: "☑ ";
            color: #28a745;
        }
        .warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>微信公众号回复功能测试</h1>
        
        <div class="warning">
            <strong>注意：</strong>此测试页面仅用于验证功能配置是否正确。实际的微信消息回复需要在微信公众号中进行测试。
        </div>

        <div class="test-section">
            <h3>1. 数据库配置检查</h3>
            <div class="test-item">
                <strong>检查数据表结构</strong>
                <span class="status pending" id="db-status">待检查</span>
                <button class="test-button" onclick="checkDatabase()">检查数据库</button>
                <div class="result-area" id="db-result"></div>
            </div>
        </div>

        <div class="test-section">
            <h3>2. 回复规则配置检查</h3>
            <div class="test-item">
                <strong>检查进采购群回复规则</strong>
                <span class="status pending" id="reply-status">待检查</span>
                <button class="test-button" onclick="checkReplyRule()">检查回复规则</button>
                <div class="result-area" id="reply-result"></div>
            </div>
        </div>

        <div class="test-section">
            <h3>3. 图片资源检查</h3>
            <div class="test-item">
                <strong>检查二维码图片</strong>
                <span class="status pending" id="image-status">待检查</span>
                <button class="test-button" onclick="checkImage()">检查图片</button>
                <div class="result-area" id="image-result"></div>
            </div>
        </div>

        <div class="test-section">
            <h3>4. 微信配置检查</h3>
            <div class="test-item">
                <strong>检查微信公众号配置</strong>
                <span class="status pending" id="wechat-status">待检查</span>
                <button class="test-button" onclick="checkWechatConfig()">检查微信配置</button>
                <div class="result-area" id="wechat-result"></div>
            </div>
        </div>

        <div class="test-section">
            <h3>5. 功能测试清单</h3>
            <div class="info">
                <strong>手动测试步骤：</strong>请在微信公众号中进行以下测试
            </div>
            <ul class="checklist">
                <li onclick="toggleCheck(this)">在微信公众号后台配置菜单，设置关键词为"进采购群"</li>
                <li onclick="toggleCheck(this)">发布菜单到微信服务器</li>
                <li onclick="toggleCheck(this)">使用测试账号关注公众号</li>
                <li onclick="toggleCheck(this)">点击"进采购群"菜单按钮</li>
                <li onclick="toggleCheck(this)">验证是否收到欢迎文本消息</li>
                <li onclick="toggleCheck(this)">验证是否收到二维码图片消息</li>
                <li onclick="toggleCheck(this)">直接发送"进采购群"文字消息测试</li>
                <li onclick="toggleCheck(this)">验证图片是否能正常显示</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>6. 问题排查</h3>
            <div class="test-item">
                <strong>常见问题及解决方案：</strong>
                <ul>
                    <li><strong>收不到回复：</strong>检查回复规则是否启用，关键词是否匹配</li>
                    <li><strong>图片显示失败：</strong>检查图片路径是否正确，文件是否存在</li>
                    <li><strong>只收到一条消息：</strong>检查第二条消息配置，确认message_count为2</li>
                    <li><strong>菜单点击无效：</strong>确认菜单已发布，关键词配置正确</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        function checkDatabase() {
            const statusEl = document.getElementById('db-status');
            const resultEl = document.getElementById('db-result');
            
            statusEl.textContent = '检查中...';
            statusEl.className = 'status pending';
            
            // 模拟检查过程
            setTimeout(() => {
                statusEl.textContent = '需要手动验证';
                statusEl.className = 'status pending';
                resultEl.innerHTML = `
                    <p><strong>请手动执行以下SQL检查：</strong></p>
                    <pre>-- 检查表结构
DESCRIBE ls_wechat_reply;

-- 检查新增字段是否存在
SHOW COLUMNS FROM ls_wechat_reply LIKE '%image_url%';
SHOW COLUMNS FROM ls_wechat_reply LIKE '%message_count%';
SHOW COLUMNS FROM ls_wechat_reply LIKE '%second_%';</pre>
                `;
            }, 1000);
        }

        function checkReplyRule() {
            const statusEl = document.getElementById('reply-status');
            const resultEl = document.getElementById('reply-result');
            
            statusEl.textContent = '检查中...';
            statusEl.className = 'status pending';
            
            setTimeout(() => {
                statusEl.textContent = '需要手动验证';
                statusEl.className = 'status pending';
                resultEl.innerHTML = `
                    <p><strong>请手动执行以下SQL检查：</strong></p>
                    <pre>-- 检查进采购群回复规则
SELECT * FROM ls_wechat_reply WHERE keyword = '进采购群' AND del = 0;</pre>
                    <p>确认返回结果包含：</p>
                    <ul>
                        <li>content_type = 3 (文本+图片)</li>
                        <li>message_count = 2 (两条消息)</li>
                        <li>second_content_type = 2 (图片)</li>
                        <li>status = 1 (启用状态)</li>
                    </ul>
                `;
            }, 1000);
        }

        function checkImage() {
            const statusEl = document.getElementById('image-status');
            const resultEl = document.getElementById('image-result');
            
            statusEl.textContent = '检查中...';
            statusEl.className = 'status pending';
            
            setTimeout(() => {
                statusEl.textContent = '需要手动验证';
                statusEl.className = 'status pending';
                resultEl.innerHTML = `
                    <p><strong>请检查以下文件是否存在：</strong></p>
                    <ul>
                        <li>public/uploads/qrcode/purchase_group_qr.jpg</li>
                        <li>确认图片格式正确（JPG/PNG）</li>
                        <li>确认图片大小合适（建议300x300像素）</li>
                        <li>确认文件大小不超过2MB</li>
                    </ul>
                `;
            }, 1000);
        }

        function checkWechatConfig() {
            const statusEl = document.getElementById('wechat-status');
            const resultEl = document.getElementById('wechat-result');
            
            statusEl.textContent = '检查中...';
            statusEl.className = 'status pending';
            
            setTimeout(() => {
                statusEl.textContent = '需要手动验证';
                statusEl.className = 'status pending';
                resultEl.innerHTML = `
                    <p><strong>请检查微信公众号配置：</strong></p>
                    <ul>
                        <li>app_id 和 secret 是否正确配置</li>
                        <li>服务器URL是否正确设置</li>
                        <li>Token是否匹配</li>
                        <li>消息加解密方式是否正确</li>
                    </ul>
                    <p><strong>测试方法：</strong>在微信公众号后台的"开发-基本配置"中点击"验证"按钮</p>
                `;
            }, 1000);
        }

        function toggleCheck(element) {
            element.classList.toggle('checked');
        }
    </script>
</body>
</html>
