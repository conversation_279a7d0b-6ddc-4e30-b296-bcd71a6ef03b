<?php


namespace app\admin\logic\shop;


use app\common\basics\Logic;
use app\common\enum\NoticeEnum;
use app\common\enum\ShopEnum;
use app\common\model\kefu\Kefu;
use app\common\model\shop\Shop;
use app\common\model\shop\ShopAdmin;
use app\common\model\shop\ShopApply;
use app\common\model\shop\ShopDeposit;
use app\admin\logic\shop\ShopDepositLogic;
use app\common\model\shop\ShopLevel;
use app\common\model\user\User;
use app\common\server\UrlServer;
use Exception;
use think\facade\Db;

class ApplyLogic extends Logic
{
    /**
     * NOTE: 获取申请列表
     * @param array $get
     * @return array
     * <AUTHOR>
     */
    public static function lists($get)
    {
        try {

            $type = [
                ['audit_status', '=', ShopEnum::AUDIT_STATUS_STAY],
                ['audit_status', '=', ShopEnum::AUDIT_STATUS_OK],
                ['audit_status', '=', ShopEnum::AUDIT_STATUS_REFUSE]
            ];
            $get['type'] = $get['type'] ?? 1;
            $where[] = $type[intval($get['type']) - 1];

            if (!empty($get['name']) and $get['name'])
                $where[] = ['name', 'like', '%'.$get['name'].'%'];

            if (!empty($get['nickname']) and $get['nickname'])
                $where[] = ['nickname', 'like', '%'.$get['nickname'].'%'];

            if (!empty($get['apply_start_time']) and $get['apply_start_time'])
                $where[] = ['apply_time', '>=', strtotime($get['apply_start_time'])];

            if (!empty($get['apply_end_time']) and $get['apply_end_time'])
                $where[] = ['apply_time', '<=', strtotime($get['apply_end_time'])];


            $model = new ShopApply();
            $lists = $model->field(true)
                ->where($where)
                ->where(['del'=>0])
                ->with(['category'])
                ->paginate([
                    'page'      => $get['page'],
                    'list_rows' => $get['limit'],
                    'var_page' => 'page'
                ])
                ->toArray();


            foreach ($lists['data'] as &$item) {
                $item['category']     = $item['category']['name'] ?? '未知类目';
                $item['audit_status_desc'] = ShopEnum::getAuditStatusDesc($item['audit_status']);

                // 添加目标等级名称
                $tierNames = [
                    0 => '0元入驻',
                    1 => '商家会员',
                    2 => '实力厂商'
                ];
                $item['target_tier_level_name'] = $tierNames[$item['target_tier_level']] ?? '0元入驻';
                $item['is_prepaid_text'] = $item['is_prepaid'] ? '已支付' : '未支付';

                $license = [];
                foreach ($item['license'] as $url) {
                    $license[] = UrlServer::getFileUrl($url);
                }

                $item['license'] = $license;
            }

            return ['count'=>$lists['total'], 'lists'=>$lists['data']];
        } catch (Exception $e) {
            return ['error'=>$e->getMessage()];
        }
    }


    /*
     * 保证金列表
     *
     */


    public static function depositList($get){
        $where = [];
        if (!empty($get['name']) and $get['name']){
            $where[] = ['name', 'like', '%'.$get['name'].'%'];
        }
        if (!empty($get['nickname']) and $get['nickname']){
            $where[] = ['nickname', 'like', '%'.$get['nickname'].'%'];
        }
        if (isset($get['status']) && $get['status'] !== ''){
            $where[] = ['status', '=', $get['status']];
        }

        // 时间筛选
        if (!empty($get['apply_start_time'])){
            $where[] = ['created_at', '>=', $get['apply_start_time']];
        }
        if (!empty($get['apply_end_time'])){
            $where[] = ['created_at', '<=', $get['apply_end_time']];
        }

        $model = new ShopDeposit();
        $lists = $model->field(true)
            ->where($where)
            ->with(['admin','details'])
            ->paginate([
                'page'      => $get['page'],
                'list_rows' => $get['limit'],
                'var_page' => 'page'
            ])->toArray();
        foreach ($lists['data'] as &$item){
            $item['name'] = $item['admin']['name'] ?? '未知';

            // 计算当前剩余保证金
            $item['current_balance'] = ShopDepositLogic::calculateCurrentBalance($item['id']);

            // 处理退款状态显示逻辑
            if ($item['refund_status'] == 1) {
                // 申请中状态，需要判断公示期
                if (!empty($item['refund_publicity_start_time']) && !empty($item['refund_publicity_end_time'])) {
                    $current_time = time();
                    $publicity_end_time = strtotime($item['refund_publicity_end_time']);

                    if ($current_time < $publicity_end_time) {
                        // 公示期内
                        $item['refund_status_display'] = '已申请(公示期)';
                        $item['refund_status_color'] = '#FFB800';
                        $item['can_refund'] = false;
                    } else {
                        // 公示期结束
                        $item['refund_status_display'] = '待退款(公示期结束)';
                        $item['refund_status_color'] = '#1E9FFF';
                        $item['can_refund'] = true;
                    }
                } else {
                    // 没有公示期信息，按原逻辑显示
                    $item['refund_status_display'] = '申请中(公示期)';
                    $item['refund_status_color'] = '#FFB800';
                    $item['can_refund'] = false;
                }
            } else {
                // 其他状态按原逻辑显示
                switch ($item['refund_status']) {
                    case 0:
                        $item['refund_status_display'] = '未申请';
                        $item['refund_status_color'] = '#999';
                        $item['can_refund'] = false;
                        break;
                    case 2:
                        $item['refund_status_display'] = '已退款';
                        $item['refund_status_color'] = '#5FB878';
                        $item['can_refund'] = false;
                        break;
                    case 3:
                        $item['refund_status_display'] = '拒绝退款';
                        $item['refund_status_color'] = '#FF5722';
                        $item['can_refund'] = false;
                        break;
                    default:
                        $item['refund_status_display'] = '未知状态';
                        $item['refund_status_color'] = '#999';
                        $item['can_refund'] = false;
                        break;
                }
            }

            // 格式化公示期结束时间显示（在状态处理完成后）
            if (!empty($item['refund_publicity_end_time'])) {
                $item['refund_publicity_end_time'] = date('Y-m-d H:i', strtotime($item['refund_publicity_end_time']));
            }
        }
        return ['count'=>$lists['total'], 'lists'=>$lists['data']];

    }
    /**
     * NOTE: 统计
     * @author: 张无忌
     * @return array
     */
    public static function totalCount()
    {
        $type = [
            ['audit_status', '=', ShopEnum::AUDIT_STATUS_STAY],
            ['audit_status', '=', ShopEnum::AUDIT_STATUS_OK],
            ['audit_status', '=', ShopEnum::AUDIT_STATUS_REFUSE]
        ];

        $model = new ShopApply();
        $ok     = $model->where(['del'=>0])->where([$type[ShopEnum::AUDIT_STATUS_OK - 1]])->count();
        $stay   = $model->where(['del'=>0])->where([$type[ShopEnum::AUDIT_STATUS_STAY - 1]])->count();
        $refuse = $model->where(['del'=>0])->where([$type[ShopEnum::AUDIT_STATUS_REFUSE - 1]])->count();

        return [
            'ok'     => $ok,
            'stay'   => $stay,
            'refuse' => $refuse
        ];

    }

    /**
     * NOTE: 详细
     * @param $id
     * @return array
     * @author: 张无忌
     */
    public static function detail($id)
    {
        $model = new ShopApply();
        $detail = $model->field(true)
            ->where(['id'=>(int)$id])
            ->with(['category'])
            ->findOrEmpty()->toArray();

        $detail['category']      = $detail['category']['name'] ?? '未知类目';
        $detail['audit_status']  = ShopEnum::getAuditStatusDesc($detail['audit_status']);
        $detail['audit_explain'] = $detail['audit_explain'] == '' ? '无' : $detail['audit_explain'];
        return $detail;
    }

    /**
     * NOTE: 审核
     * @param $post
     * @return bool
     * @author: 张无忌
     */
    public static function audit($post)
    {
        Db::startTrans();
        try {
            ShopApply::update([
                'audit_status'  => $post['audit_status'],
                'audit_explain' => $post['audit_explain'] ?? ''
            ], ['id'=>(int)$post['id']]);

            $model = new ShopApply();
            $shopApply = $model->field(true)->findOrEmpty((int)$post['id'])->toArray();

            if ($post['audit_status'] == ShopEnum::AUDIT_STATUS_OK) {
                // 获取申请的目标等级，如果没有则默认为0元入驻
                $targetTierLevel = $shopApply['target_tier_level'] ?? 0;

                // 计算等级到期时间
                $tierExpireTime = 0;
                if ($targetTierLevel > 0) {
                    // 获取等级配置
                    $tierConfig = \app\common\model\shop\ShopTierConfig::getTierConfig($targetTierLevel);
                    if ($tierConfig) {
                        $tierExpireTime = time() + ($tierConfig['duration_days'] * 24 * 3600);
                    }
                }

                // 新增商家信息
                $shop = Shop::create([
                    'cid'      => $shopApply['cid'],
                    'type'     => ShopEnum::SHOP_TYPE_IN,
                    'name'     => $shopApply['name'],
                    'nickname' => $shopApply['nickname'],
                    'mobile'   => $shopApply['mobile'],
                    'tier_level' => $targetTierLevel,
                    'tier_expire_time' => $tierExpireTime,
                    'tier_upgrade_time' => time(),
                    'logo'              => 'https://huohanghang.oss-cn-beijing.aliyuncs.com//uploads/images/20250610/202506101917136c6a06430.png',
                    'background'        => '',
                    'business_license'  => $shopApply['license'],
                    'keywords'          => '',
                    'intro'             => '',
                    'weight'            => 0,
                    'trade_service_fee' => 0,
                    'is_run'            => ShopEnum::SHOP_RUN_CLOSE,
                    'is_freeze'         => ShopEnum::SHOP_FREEZE_NORMAL,
                    'is_product_audit'  => ShopEnum::PRODUCT_AUDIT_TRUE,
                    'is_recommend'      => ShopEnum::SHOP_RECOMMEND_FALSE,
                    'del'               => 0,
                    'expire_time'       => 0,
                ]);

                // 新增商家登录账号
                $time = time();
                $salt = substr(md5($time . $shopApply['name']), 0, 4);//随机4位密码盐
                $shop_info=ShopAdmin::where('account', $shopApply['account'])->find();
                if(empty($shop_info)){
                    $shop_admin=ShopAdmin::create([
                    'shop_id' => $shop->id,
                    'name' => '超级管理员',
                    'account' => $shopApply['account'],
                    'password' => generatePassword($shopApply['password'], $salt),
                    'salt' => $salt,
                    'role_id' => 0,
                    'create_time' => $time,
                    'update_time' => $time,
                    'disable' => 0,
                    'del' => 0
                   ]);

                   //增加默认客服
                Kefu::create([

                    'shop_id' => $shop->id,
                    'admin_id' => $shop_admin->id,
                    'nickname' =>'超级管理员',
                    'avatar' => UrlServer::setFileUrl(Db::name('user')->where(['id' => $shopApply['user_id']])->value('avatar')),
                    'disable' => 0,
                    'sort' => 1,
                    'create_time' => time(),
                ]);
                }
               

                

                // 设置默认客服配置为在线客服
                \app\common\server\ConfigServer::set('shop_customer_service', 'type', 2, $shop->id);



                //更新用户身份状态
                User::update([
                    'shop_id' => $shop->id,
                ], ['id'=>$shopApply['user_id']]);

                //成功通知
                event('Notice', [
                    'scene' => NoticeEnum::SHOP_APPLY_SUCCESS_NOTICE,
                    'mobile' => $shopApply['mobile'],
                    'params' => [
                        'user_id'           => $shopApply['user_id'],
                        'shop_name'         => $shopApply['name'],
                        // 'shop_admin_url'    => request()->domain().'/shop',
                        'shop_admin_account' => $shopApply['account'],
                    ]
                ]);
            } else {
                //失败通知
                event('Notice', [
                    'scene' => NoticeEnum::SHOP_APPLY_ERROR_NOTICE,
                    'mobile' => $shopApply['mobile'],
                    'params' => [
                        'user_id'           => $shopApply['user_id'],
                        'shop_name'         => $shopApply['name'],
                    ]
                ]);
            }
            Db::commit();
            return $shop->id;
        } catch (Exception $e) {
            Db::rollback();
            // var_dump($e->getMessage());die;
            static::$error = $e->getMessage();

            return false;
        }
    }
    public static function getUserLevel($id){
        $detail = ShopLevel::where(['id'=>$id,'del'=>0])->findOrEmpty();
        if($detail->isEmpty()) {
            return [];
        }
        $detail = $detail->toArray();
        $detail['image'] = UrlServer::getFileUrl($detail['image']);
      
//        $detail['background_image'] = UrlServer::getFileUrl($detail['background_image']);
        return $detail;
    }
    public static function edit($post)
    {

        Db::startTrans();
        try{
            $userLevel = ShopLevel::where([
                ['name', '=', trim($post['name'])],
                ['del', '=', 0],
                ['id', '<>', $post['id']]
            ])->findOrEmpty();
            if(!$userLevel->isEmpty()) {
                throw new \think\Exception('等级名称已被使用，请更换后重试');
            }

            $time = time();
            $data = [
                'id' => $post['id'],
                'name' => trim($post['name']),
//                'hdiscount' => $post['hdiscount'],
                'image' => clearDomain($post['image']),
//                'discount' => $post['discount2'],
                'remark' => trim($post['remark']),
                'update_time' => $time,
                'del' => 0
            ];
            ShopLevel::update($data);

            Db::commit();
            return true;
        }catch(\Exception $e) {
            Db::rollback();
            self::$error = $e->getMessage();
            return false;
        }
    }
    public static function add($post)
    {
        Db::startTrans();
        try{
            $userLevel = ShopLevel::where(['name'=>trim($post['name']),'del'=>0])->findOrEmpty();
            if(!$userLevel->isEmpty()) {
                throw new \think\Exception('等级名称已被使用，请更换后重试');
            }
            $time = time();
            $data = [
                'name' => trim($post['name']),
                'image' => clearDomain($post['image']),
//                'background_image' => clearDomain($post['background_image']),
                'remark' => trim($post['remark']),
//                'discount' => $post['discount2'],
//                'privilege' => $post['privilege'],
//                'hdiscount' => $post['hdiscount'],
                'create_time' => $time,
                'update_time' => $time,
                'del' => 0
            ];
            ShopLevel::create($data);
            // 更新会员等级
//            $userArr = User::field('id,level,user_growth')->where('del', 0)->select()->toArray();
//            self::updateUserLevel($userArr);
            Db::commit();
            return true;
        }catch(\Exception $e) {
            Db::rollback();
            self::$error = $e->getMessage();
            return false;
        }
    }

    /**
     * NOTE: 删除
     * @author: 张无忌
     * @param $id
     * @return bool
     */
    public static function del($id)
    {
        try {
            ShopLevel::update([
                'del' => 1,
                'update_time' => time()
            ], ['id'=>(int)$id]);

            return true;
        } catch (Exception $e) {
            static::$error = $e->getMessage();
            return false;
        }
    }


    /*
     * 商家登记列表
     *
     */
    public static function levelList($get){
        $count = ShopLevel::where(['del'=>0])->count();
        $lists = ShopLevel::where(['del'=>0])->order('id', 'asc')->page($get['page'], $get['limit'])->select()->toArray();

        foreach ($lists as &$item){
            $item['image'] = UrlServer::getFileUrl($item['image']);
            $item['background_image'] = UrlServer::getFileUrl($item['background_image']);
        }
        return ['count' => $count, 'lists' => $lists];
    }
}