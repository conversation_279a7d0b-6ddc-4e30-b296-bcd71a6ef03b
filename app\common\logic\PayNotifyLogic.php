<?php


namespace app\common\logic;

use app\admin\logic\distribution\DistributionLevelLogic;
use app\admin\logic\shop\ApplyLogic;
use app\api\logic\PayLogic;
use app\common\enum\IntegralGoodsEnum;
use app\common\enum\IntegralOrderEnum;
use app\common\enum\NoticeEnum;
use app\common\enum\ShopTierEnum;
use app\common\model\{
    AccountLog,
    AdOrder,
    agent\AgentMerchantfees,
    agent\AgentRelationship,
    goods\Goods,
    integral\IntegralGoods,
    integral\IntegralOrder,
    jcai\JcaiOrder,
    order\OrderGoods,
    RechargeOrder,
    shop\Shop,
    shop\ShopApply,
    shop\ShopDeposit,
    shop\ShopMerchantfees,
    shop\ShopDepositDetails
};
use app\common\enum\OrderEnum;
use app\common\enum\OrderLogEnum;
use app\common\enum\PayEnum;
use app\common\model\order\Order;
use app\common\model\order\OrderTrade;
use app\common\server\DistributionServer;
use app\common\model\order\OrderLog;
use app\common\model\user\User;
use app\common\server\ConfigServer;
use think\facade\Db;
use think\Exception;
use think\facade\Log;
use app\admin\logic\agent\AgentLogic;
use app\common\logic\{
    AccountLogLogic,
    CommissionLogic,
    GoodsVirtualLogic
};

/**
 * 支付成功后处理订单状态
 * Class PayNotifyLogic
 * @package app\api\logic
 */
class PayNotifyLogic
{
    /**
     * @notes 回调处理
     * @param $action
     * @param $order_sn
     * @param array $extra
     * @return bool|string
     * @throws \think\exception\PDOException
     * <AUTHOR>
     * @date 2021/7/13 6:32 下午
     */
    public static function handle($action, $order_sn, $extra = [])
    {
        // 记录开始处理支付回调
        Db::name('log')->insert([
            'type' => 'handle_start',
            'log' => "开始处理支付回调，类型：{$action}，订单号：{$order_sn}",
            'creat_time' => date('Y-m-d H:i:s')
        ]);

        // 检查方法是否存在
        if (!method_exists(self::class, $action)) {
            $error_msg = "支付回调处理方法不存在：{$action}";
            Db::name('log')->insert([
                'type' => 'handle_error',
                'log' => $error_msg,
                'creat_time' => date('Y-m-d H:i:s')
            ]);
            return $error_msg;
        }

        Db::startTrans();
        try {
            self::$action($order_sn, $extra);
            Db::commit();

            // 记录处理成功
            Db::name('log')->insert([
                'type' => 'handle_success',
                'log' => "支付回调处理成功，类型：{$action}，订单号：{$order_sn}",
                'creat_time' => date('Y-m-d H:i:s')
            ]);

            return true;
        } catch (Exception $e) {
            Db::rollback();

            // 记录详细的异常信息
            $error_info = [
                'action' => $action,
                'order_sn' => $order_sn,
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ];

            Db::name('log')->insert([
                'type' => 'handle_exception',
                'log' => json_encode($error_info),
                'creat_time' => date('Y-m-d H:i:s')
            ]);

            $record = [
                __CLASS__, __FUNCTION__, $e->getFile(), $e->getLine(), $e->getMessage()
            ];
            Log::write(implode('-', $record));

            return $e->getMessage();
        }
    }


    /**
     * @notes 添加订单日志表
     * @param $order_id
     * @param $user_id
     * @param $shop_id
     * @return array
     * <AUTHOR>
     * @date 2021/7/13 6:32 下午
     */
    public static function getOrderLogData($order_id, $user_id, $shop_id)
    {
        $order_log_data = [];
        $order_log_data['type'] = OrderLogEnum::TYPE_USER;
        $order_log_data['channel'] = OrderLogEnum::USER_PAID_ORDER;
        $order_log_data['order_id'] = $order_id;
        $order_log_data['handle_id'] = $user_id;
        $order_log_data['shop_id'] = $shop_id;
        $order_log_data['content'] = OrderLogEnum::getLogDesc(OrderLogEnum::USER_PAID_ORDER);
        $order_log_data['create_time'] = time();
        return $order_log_data;
    }


    /**
     * @notes 父订单回调
     * @param $order_sn
     * @param array $extra
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     * <AUTHOR>
     * @date 2021/7/13 6:33 下午
     */
    private static function trade($order_sn, $extra = [])
    {
        //根据父订单号查找父订单
        $trade = OrderTrade::where(['t_sn' => $order_sn])->find();
        //根据父订单id查找子订单
        $orders = Order::with(['order_goods', 'shop'])
            ->where('trade_id', $trade['id'])
            ->select()->toArray();
        //修改用户消费累计额度
        $user = User::find($trade['user_id']);
        $user->total_order_amount = ['inc', $trade['order_amount']];
        $user->save();

        //赠送成长值
        $growth_ratio = ConfigServer::get('transaction', 'money_to_growth', 0);
        if ($growth_ratio > 0) {
            $able_get_growth = floor($trade['total_amount'] / $growth_ratio);
            $user->where('id', $trade['user_id'])
                ->inc('user_growth', $able_get_growth)
                ->update();
            AccountLogLogic::AccountRecord($trade['user_id'], $able_get_growth, 1, AccountLog::order_give_growth, '', $trade['id'], $order_sn);
        }

        // 生成分销订单
        PayLogic::distributionOrderGoods(array_column($orders, 'id'));
        // 更新分销会员等级
        DistributionLevelLogic::updateDistributionLevel($trade['user_id']);

        foreach ($orders as $item) {

            //赠送积分
            $open_award = ConfigServer::get('order_award', 'open_award', 0);
            if ($open_award == 1) {
                $award_event = ConfigServer::get('order_award', 'award_event', 0);
                $award_ratio = ConfigServer::get('order_award', 'award_ratio', 0);
                if ($award_ratio > 0) {
                    $award_integral = floor($item['order_amount'] * ($award_ratio / 100));
                }
            }

            $orderStatus = Order::STATUS_WAIT_DELIVERY;
            //线下自提订单支付完成后订单状态改为待收货
            if ($item['delivery_type'] == OrderEnum::DELIVERY_TYPE_SELF) {
                $orderStatus = Order::STATUS_WAIT_RECEIVE;
            }
            //更新订单状态
            $data = [
                'pay_status' => PayEnum::ISPAID,
                'pay_time' => time(),
                'order_status' => $orderStatus,
                'award_integral_status' => $award_event ?? 0,
                'award_integral' => $award_integral ?? 0
            ];

            //如果返回了第三方流水号
            if (isset($extra['transaction_id'])) {
                $data['transaction_id'] = $extra['transaction_id'];
            }
            $orderUpdate = Order::update($data, [
                [ 'id', '=', $item['id'] ],
                [ 'pay_status', '=', PayEnum::UNPAID ],
            ]);
            if ($orderUpdate->getUpdateResult() <= 0) {
                throw new \Exception('修改订单状态失败，订单可能已支付');
            }

            // 增加一条订单日志
            $order_log_add_data = self::getOrderLogData($item['id'], $item['user_id'], $item['shop_id']);
            $order_log_datas_insert[] = $order_log_add_data;
            OrderLog::insertAll($order_log_datas_insert);

//            if ($item['order_type'] == order::NORMAL_ORDER){
//                DistributionServer::commission($item['id']);
//            }
            //通知用户
            event('Notice', [
                'scene' => NoticeEnum::ORDER_PAY_NOTICE,
                'mobile' => $item['mobile'] ?? '',
                'params' => ['order_id' => $item['id'], 'user_id' => $item['user_id']]
            ]);

            //通知商家
            if (!empty($item['shop']['mobile'])) {
                event('Notice', [
                    'scene' => NoticeEnum::USER_PAID_NOTICE_SHOP,
                    'mobile' => $item['shop']['mobile'],
                    'params' => ['order_id' => $item['id'], 'user_id' => $item['user_id']]
                ]);
            }

            event('Printer', [
                'order_id' => $item['id'],
            ]);
        }
        //如果返回了第三方流水号
        if (isset($extra['transaction_id'])) {
            $trade->transaction_id = $extra['transaction_id'];
            $trade->save();
        }

        $order_ids = array_column($orders, 'id');

        // 虚拟商品更新订单信息
        GoodsVirtualLogic::afterPayVirtualDelivery($order_ids);

        // 更新商品销量
        self::updateGoodsSales($order_ids);

        // 更新订单商品佣金比例
        foreach ($order_ids as $order_id) {
            CommissionLogic::updateOrderGoodsCommission($order_id);
        }
    }

    /**
     * @notes 子订单回调
     * @param $order_sn
     * @param array $extra
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     * <AUTHOR>
     * @date 2021/7/13 6:33 下午
     */
    private static function order($order_sn, $extra = [])
    {

        $time = time();
        $order = Order::with(['order_goods', 'shop'])
            ->where('order_sn', $order_sn)
            ->find()->toArray();

        //赠送积分
        $open_award = ConfigServer::get('order_award', 'open_award', 0);
        if ($open_award == 1) {
            $award_event = ConfigServer::get('order_award', 'award_event', 0);
            $award_ratio = ConfigServer::get('order_award', 'award_ratio', 0);
            if ($award_ratio > 0) {
                $award_integral = floor($order['order_amount'] * ($award_ratio / 100));
            }
        }

        $orderStatus = Order::STATUS_WAIT_DELIVERY;
        //线下自提订单支付完成后订单状态改为待收货
        if ($order['delivery_type'] == OrderEnum::DELIVERY_TYPE_SELF) {
            $orderStatus = Order::STATUS_WAIT_RECEIVE;
        }
        //更新订单状态
        $data = [
            'pay_status' => PayEnum::ISPAID,
            'pay_time' => time(),
            'order_status' => $orderStatus,
            'award_integral_status' => $award_event ?? 0,
            'award_integral' => $award_integral ?? 0
        ];
        //如果返回了第三方流水号
        if (isset($extra['transaction_id'])) {
            $data['transaction_id'] = $extra['transaction_id'];
        }
        $orderUpdate = Order::update($data, [
            [ 'id', '=', $order['id'] ],
            [ 'pay_status', '=', PayEnum::UNPAID ],
        ]);
        if ($orderUpdate->getUpdateResult() <= 0) {
            throw new \Exception('修改订单状态失败，订单可能已支付');
        }

        // 增加商品销量

        // 增加一条订单日志
        $order_log_add_data = self::getOrderLogData($order['id'], $order['user_id'], $order['shop_id']);
        $order_log_datas_insert[] = $order_log_add_data;
        OrderLog::insertAll($order_log_datas_insert);

        //修改用户消费累计额度
        $user = User::find($order['user_id']);
        $user->total_order_amount = ['inc', $order['order_amount']];
        $user->save();

        //赠送成长值6
        $growth_ratio = ConfigServer::get('transaction', 'money_to_growth', 0);
        if ($growth_ratio > 0) {
            $able_get_growth = floor($order['order_amount'] / $growth_ratio);
            $user->where('id', $order['user_id'])
                ->inc('user_growth', $able_get_growth)
                ->update();
            AccountLogLogic::AccountRecord($order['user_id'], $able_get_growth, 1, AccountLog::order_give_growth, '', $order['id'], $order_sn);
        }

        // 生成分销订单
        PayLogic::distributionOrderGoods([$order['id']]);
        // 更新分销会员等级
        DistributionLevelLogic::updateDistributionLevel($order['user_id']);

//        //拼购,砍价的订单不参与分销分佣
//        if ($order['order_type'] == order::NORMAL_ORDER){
//            DistributionServer::commission($order['id']);
//        }

        // 虚拟商品更新订单信息
        GoodsVirtualLogic::afterPayVirtualDelivery($order['id']);

        // 更新商品销量
        self::updateGoodsSales($order['id']);

        // 更新订单商品佣金比例
        CommissionLogic::updateOrderGoodsCommission($order['id']);

        // 添加活跃度积分（购买商品）
        try {
            \app\common\logic\UserActivityLogic::addActivityScore(
                $order['user_id'],
                \app\common\enum\UserActivityEnum::ACTIVITY_PURCHASE,
                ['order_id' => $order['id'], 'order_amount' => $order['order_amount']]
            );
        } catch (\Exception $e) {
            // 活跃度系统异常不影响订单流程，记录日志即可
            trace('用户活跃度检查失败: ' . $e->getMessage(), 'error');
        }

        //通知用户
        event('Notice', [
            'scene' => NoticeEnum::ORDER_PAY_NOTICE,
            'mobile' => $order['mobile'] ?? '',
            'params' => ['order_id' => $order['id'], 'user_id' => $order['user_id']]
        ]);

        //通知商家
        if (!empty($order['shop']['mobile'])) {
            event('Notice', [
                'scene' => NoticeEnum::USER_PAID_NOTICE_SHOP,
                'mobile' => $order['shop']['mobile'],
                'params' => ['order_id' => $order['id'], 'user_id' => $order['user_id']]
            ]);
        }

        event('Printer', [
            'order_id' => $order['id'],
        ]);
    }


    /**
     * @notes 充值回调
     * @param $order_sn
     * @param array $extra
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     * <AUTHOR>
     * @date 2021/7/13 6:33 下午
     */
    private static function recharge($order_sn, $extra = [])
    {

        $new = time();
        $recharge_order = new RechargeOrder();
        $order = $recharge_order->where(['order_sn' => $order_sn])->find();
        $update_data['pay_time'] = $new;
        $update_data['pay_status'] = PayEnum::ISPAID;
        if (isset($extra['transaction_id'])) {
            $update_data['transaction_id'] = $extra['transaction_id'];
        }
        $recharge_order->where(['id' => $order['id']])->update($update_data);
        $user = User::find($order['user_id']);
        $total_money = $order['order_amount'] + $order['give_money'];
        $total_integral = $order['give_integral'];
        $user->user_money = ['inc', $total_money];
        $user->user_integral = ['inc', $total_integral];
        $user->user_growth = ['inc', $order['give_growth']];
        $user->total_recharge_amount = ['inc', $total_money];
        $user->save();
        //记录余额
        $total_money > 0 && AccountLogLogic::AccountRecord($user->id, $total_money, 1, AccountLog::recharge_money, '', $order['id'], $order_sn);
        //记录积分
        $total_integral > 0 && AccountLogLogic::AccountRecord($user->id, $total_integral, 1, AccountLog::recharge_give_integral);
        //记录成长值
        $order['give_growth'] > 0 && AccountLogLogic::AccountRecord($user->id, $order['give_growth'], 1, AccountLog::recharge_give_growth);
    }





    /**
     * @notes 积分订单回调
     * @param $order_sn
     * @param array $extra
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date 2022/3/1 14:36
     */
    private static function integral($order_sn, $extra = [])
    {
        $order = IntegralOrder::where(['order_sn' => $order_sn])->findOrEmpty();
        $goods = $order['goods_snap'];

        // 更新订单状态
        $data = [
            'order_status' => IntegralOrderEnum::ORDER_STATUS_DELIVERY,
            'pay_status' => PayEnum::ISPAID,
            'pay_time' => time(),
        ];
        // 红包类型 或者 无需物流 支付完即订单完成
        if ($goods['type'] == IntegralGoodsEnum::TYPE_BALANCE || $goods['delivery_way'] == IntegralGoodsEnum::DELIVERY_NO_EXPRESS) {
            $data['order_status'] = IntegralOrderEnum::ORDER_STATUS_COMPLETE;
            $data['confirm_time'] = time();
        }
        // 第三方流水号
        if (isset($extra['transaction_id'])) {
            $data['transaction_id'] = $extra['transaction_id'];
        }
        IntegralOrder::update($data, ['id' => $order['id']]);

        // 更新商品销量
        IntegralGoods::where([['id', '=', $goods['id']], ['stock', '>=', $order['total_num']]])
            ->dec('stock', $order['total_num'])
            ->inc('sales', $order['total_num'])
            ->update();

        // 红包类型，直接增加余额
        if ($goods['type'] == IntegralGoodsEnum::TYPE_BALANCE) {
            $reward = round($goods['balance'] * $order['total_num'], 2);
            User::where(['id' => $order['user_id']])
                ->inc('user_money', $reward)
                ->update();

            AccountLogLogic::AccountRecord(
                $order['user_id'],
                $reward, 1,
                AccountLog::integral_order_inc_balance,
                '', $order['id'], $order['order_sn']
            );
        }

    }


    /**
     * @notes 更新商品销量
     * @param $order_id
     * @return bool
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date 2022/10/14 10:16
     */
    private static function updateGoodsSales($order_ids)
    {
        if (!is_array($order_ids)) {
            $order_ids = [$order_ids];
        }

        $order_goods = OrderGoods::whereIn('order_id', $order_ids)
            ->select()
            ->toArray();

        if (empty($order_goods)) {
            return false;
        }

        foreach ($order_goods as $item) {
            // 增加商品销量
            Goods::where('id', $item['goods_id'])
                ->inc('sales_actual', $item['goods_num'])
                ->update();
        }
        return true;
    }


    /**
     * @notes 入驻费检验费回调
     * @param $order_sn
     * @param array $extra
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     * <AUTHOR>
     * @date 2021/7/13 6:33 下午
     */
    private static function ruzhucharge($order_sn, $extra = [])
    {
        // 记录回调开始
        Db::name('log')->insert(['type'=>'ruzhucharge_start','log'=>'开始处理商家入驻费回调，订单号：'.$order_sn,'creat_time'=>date('Y-m-d H:i:s')]);

        try {
            Db::startTrans();
            $recharge_order = new ShopMerchantfees();
            $order = $recharge_order->where(['order_sn' => $order_sn])->find();

            if (!$order) {
                Db::name('log')->insert(['type'=>'ruzhucharge_error','log'=>'订单不存在：'.$order_sn,'creat_time'=>date('Y-m-d H:i:s')]);
                throw new \Exception('订单不存在');
            }

            // 记录订单信息
            Db::name('log')->insert(['type'=>'ruzhucharge_order','log'=>'找到订单：'.json_encode($order->toArray()),'creat_time'=>date('Y-m-d H:i:s')]);

            // 更新支付状态
            $update_data['status'] = PayEnum::ISPAID;
            $update_data['payment_date'] = time();
            if (isset($extra['transaction_id'])) {
                $update_data['transaction_id'] = $extra['transaction_id'];
            }
            $recharge_order->where(['id' => $order['id']])->update($update_data);

            // 判断是商家等级升级还是新入驻
            if (isset($order['tier_type']) && $order['tier_type'] == ShopTierEnum::TYPE_UPGRADE) {
                // 商家等级升级逻辑
                self::handleShopTierUpgrade($order);
                // 升级也要处理代理费佣金
                $order_type = 5; // 升级订单类型
                self::handleAgentCommission($order, $order_type);
            } else {
                // 商家入驻逻辑
                $order_type = self::handleShopEntry($order);
                // 处理代理费佣金
                self::handleAgentCommission($order, $order_type);
            }

            // 记录支付成功日志
            Db::name('log')->insert([
                'type' => 'shop_payment_success',
                'log' => "商家支付成功，用户ID:{$order['user_id']}, 订单号:{$order_sn}, 金额:{$order['amount']}, 类型:" . (isset($order['tier_type']) && $order['tier_type'] == ShopTierEnum::TYPE_UPGRADE ? '等级升级' : '新入驻') . "，已处理代理佣金",
                'creat_time' => date('Y-m-d H:i:s')
            ]);

            Db::commit();
        }catch (\Exception $e){
            Db::rollback();
           return $e->getMessage();
        }
    }

    /**
     * 处理商家等级升级
     */
    private static function handleShopTierUpgrade($order)
    {
        if ($order['shop_id'] > 0) {
            $shop = \app\common\model\shop\Shop::find($order['shop_id']);
            if ($shop) {
                $shop->tier_level = $order['tier_level'];
                $shop->tier_expire_time = time() + (365 * 24 * 3600); // 1年后到期
                $shop->save();

                // 记录升级日志
                Db::name('log')->insert([
                    'type' => 'tier_upgrade',
                    'log' => "商家ID:{$shop->id} 等级升级到:{$order['tier_level']}",
                    'creat_time' => date('Y-m-d H:i:s')
                ]);
            }
        }
    }

    /**
     * 处理商家入驻
     */
    private static function handleShopEntry($order)
    {
        $shop = new ShopApply();
        $post['id'] = $shop->where(['user_id' => $order['user_id']])->value('id');
        $post['audit_status'] = 2;
        $post['audit_explain'] = '已支付';
        ApplyLogic::audit($post);

        $shop_id = Db::name('user')->where(['id' => $order['user_id']])->value('shop_id');
        $shop_data['is_run'] = 1;

        if ($order['feetype'] == 1) {
            // 实力厂商 入驻费和检验费同时交
            $shop_data['yan_fee'] = 1;
            $shop_data['entry_time'] = ['inc', strtotime('+1 year')]; // 入驻费到期时间
            $shop_data['expire_time'] = ['inc', strtotime('+1 year')]; // 到期时间
            $shop_data['yan_time'] = ['inc', strtotime('+1 year')];
            $order_type = 4;
        } else if ($order['feetype'] == 0) {
            // 商家会员 只交入驻费
            $shop_data['entry_time'] = ['inc', strtotime('+1 year')];
            $shop_data['expire_time'] = ['inc', strtotime('+1 year')];
            $order_type = 2;
        } else {
            // 只交检验费
            $shop_data['yan_fee'] = 1;
            $shop_data['expire_time'] = ['inc', strtotime('+1 year')]; // 到期时间
            $order_type = 3;
        }

        Db::name('shop')->where(['id' => $shop_id])->update($shop_data);
        Db::name('shop_merchantfees')->where(['order_sn' => $order['order_sn']])->update(['shop_id' => $shop_id]);

        return $order_type;
    }

    /**
     * 处理代理费佣金
     */
    private static function handleAgentCommission($order, $order_type)
    {
        // 处理代理费佣金
        $f_user_id = AgentLogic::getAgentSupervisor($order['user_id']);

        if (!empty($f_user_id)) {
            // 检查一级代理是否申请保证金退款 (假设 agent_merchantfees 有 0未支付1已支付2退款中3已退款)
            $f_user_refund_status = Db::name('agent_merchantfees')->where('user_id', $f_user_id)->where('status', 1)->find();

            
            $first_ratio = ConfigServer::get('agent', 'first_ratio', 0);
            $two_ratio = ConfigServer::get('agent', 'two_ratio', 0);
            $t_user_id = AgentLogic::getAgentSupervisor($f_user_id);

            // 判断一级代理是否有效 (状态正常且未申请退款)
            $is_f_user_valid = !empty($f_user_refund_status);
            if ($is_f_user_valid) {
                // --- 一级代理有效，按原逻辑处理 ---
                // 直属代理 获得佣金
                $first_order = []; // 初始化数组
                $first_order['sn'] = 'F' . $order['order_sn'];
                $first_order['user_id'] = $f_user_id;
                $first_order['order_type'] = $order_type;
                $first_order['g_user_id'] = $order['user_id'];
                $first_order['p_user_id'] = 0; // 顶级代理的上级是平台
                $first_order['level'] = 1;
                $first_order['ratio'] = round($first_ratio / 100, 2);
                $first_order['commissions_id'] = $order['id'];
                $first_order['money'] = $order['amount'] * $first_order['ratio'];
                $first_order['buy_time'] = strtotime('+1 year');
                $first_order['create_time'] = time();
                $first_order['order_total'] = $order['amount'];
                $first_order['pt_get'] = $order['amount'] - $first_order['money']; // 初始平台收益

                // 处理二级代理
                if (!empty($t_user_id)) {
                    // 获取二级代理状态
                    $t_user_refund_status = Db::name('agent_merchantfees')->where('user_id', $t_user_id)->where('status', 1)->find();
                    $is_t_user_valid = !empty($t_user_refund_status);

                    if ($is_t_user_valid) {
                        $two_order = []; // 初始化数组
                        $two_order['sn'] = 'T' . $order['order_sn'];
                        $two_order['user_id'] = $t_user_id;
                        $two_order['p_user_id'] = $f_user_id; // 二级代理的上级是一级代理
                        $two_order['g_user_id'] = $order['user_id']; // 产生佣金的用户
                        $two_order['level'] = 2; // 二级代理
                        $two_order['order_type'] = $order_type;
                        $two_order['ratio'] = round($two_ratio / 100, 2);
                        $two_order['money'] = $order['amount'] * $two_order['ratio'];
                        $two_order['commissions_id'] = $order['id'];
                        $two_order['buy_time'] = strtotime('+1 year');
                        $two_order['create_time'] = time();
                        $two_order['order_total'] = $order['amount'];
                        // 平台收益 = 总金额 - 一级佣金 - 二级佣金
                        $two_order['pt_get'] = $order['amount'] - $first_order['money'] - $two_order['money'];
                        $first_order['pt_get'] = $two_order['pt_get']; // 更新一级订单记录里的平台收益
                        $two_order_id = Db::name('agent_order')->strict(false)->insertGetId($two_order);
                        if ($two_order_id && $two_order['money'] > 0) {
                            AccountLogLogic::AccountRecord($t_user_id, $two_order['money'], 1, AccountLog::distribution_inc_earnings, '', $two_order_id, $two_order['sn']);
                        }
                    } else {
                        // 二级代理无效，平台收益 = 总金额 - 一级佣金
                        $first_order['pt_get'] = $order['amount'] - $first_order['money'];
                    }
                }
                // 插入一级代理订单
                $first_order_id = Db::name('agent_order')->strict(false)->insertGetId($first_order);
                if ($first_order_id && $first_order['money'] > 0) {
                    AccountLogLogic::AccountRecord($f_user_id, $first_order['money'], 1, AccountLog::distribution_inc_earnings, '', $first_order_id, $first_order['sn']);
                }
            }
        }
    }



    /**
     * @notes 集采购会员费回调
     * @param $order_sn
     * @param array $extra
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     * <AUTHOR>
     * @date 2021/7/13 6:33 下午
     */
    private static function rejcharge($order_sn, $extra = [])
    {

        try{
            Db::startTrans();
            $recharge_order = new JcaiOrder();
            $order = $recharge_order->where(['order_sn' => $order_sn])->find()->toArray();
            $order['order_amount']=1000;
            $update_data['pay_status'] = PayEnum::ISPAID;
            $update_data['pay_time'] = time();
            $recharge_order->where(['id' => $order['id']])->update($update_data);
            $user = User::find($order['user_id']);
            $user2['jcvip']=1;
            if(empty($user['vip_time'])){
                $user['vip_time']=strtotime(date('Y-m-d'));
            }
            $user2['vip_time']=$user['vip_time']+$order['buy_time'];
            Db::name('user')->where(['id' => $order['user_id']])->update($user2);
            //处理代理费佣金
            $f_user_id = AgentLogic::getAgentSupervisor($order['user_id']);
            if (!empty($f_user_id)) {
                // 检查一级代理是否申请保证金退款 (假设 agent_merchantfees 有 status 为1时有效)
                // 注意：这里使用的是 shop_deposit 表，与 rejcharge 的 agent_merchantfees 不同，需要确认业务逻辑
                // 使用 agent_merchantfees
                $f_user_refund_status = Db::name('agent_merchantfees')->where('user_id', $f_user_id)->where('status', 1)->find();
                $first_ratio = ConfigServer::get('agent', 'first_ratio', 0);
                $two_ratio = ConfigServer::get('agent', 'two_ratio', 0);
                $t_user_id = AgentLogic::getAgentSupervisor($f_user_id);
                // 判断一级代理是否有效 (状态正常且未申请退款)
                $is_f_user_valid = !empty($f_user_refund_status);
                if ($is_f_user_valid) {
                    // --- 一级代理有效，按原逻辑处理 ---
                    //直属代理 获得佣金
                    $first_order = []; // 初始化数组
                    $first_order['sn'] = 'F' . $order['order_sn'];
                    $first_order['user_id'] = $f_user_id;
                    $first_order['order_type'] = 1; // 集采订单类型
                    $first_order['g_user_id'] = $order['user_id'];
                    $first_order['p_user_id'] = 0; // 顶级代理的上级是平台
                    $first_order['level'] = 1;
                    $first_order['ratio'] = round($first_ratio / 100, 2);
                    $first_order['commissions_id'] = $order['id'];
                    $first_order['money'] = $order['order_amount'] * $first_order['ratio'];
                    $first_order['buy_time'] = $order['buy_time'];
                    $first_order['create_time'] = time();
                    $first_order['order_total'] = $order['order_amount'];
                    $first_order['pt_get'] = $order['order_amount'] - $first_order['money']; // 初始平台收益
                    // 处理二级代理
                    if (!empty($t_user_id)) {
                         // 获取二级代理状态
                        $t_user_refund_status = Db::name('agent_merchantfees')->where('user_id', $t_user_id)->where('status',1)->find();
                        $is_t_user_valid = !empty($t_user_refund_status);
                        if ($is_t_user_valid) {
                            $two_order = []; // 初始化数组
                            $two_order['sn'] = 'T' . $order['order_sn'];
                            $two_order['user_id'] = $t_user_id;
                            $two_order['p_user_id'] = $f_user_id; // 二级代理的上级是一级代理
                            $two_order['g_user_id'] = $order['user_id']; // 产生佣金的用户
                            $two_order['level'] = 2; // 二级代理
                            $two_order['order_type'] = 1; // 集采订单类型
                            $two_order['ratio'] = round($two_ratio / 100, 2);
                            $two_order['money'] = $order['order_amount'] * $two_order['ratio'];
                            $two_order['commissions_id'] = $order['id'];
                            $two_order['buy_time'] = $order['buy_time'];
                            $two_order['create_time'] = time();
                            $two_order['order_total'] = $order['order_amount'];
                            // 平台收益 = 总金额 - 一级佣金 - 二级佣金
                            $two_order['pt_get'] = $order['order_amount'] - $first_order['money'] - $two_order['money'];
                            $first_order['pt_get'] = $two_order['pt_get']; // 更新一级订单记录里的平台收益

                            $two_order_id = Db::name('agent_order')->strict(false)->insertGetId($two_order);
                            if ($two_order_id && $two_order['money'] > 0) {
                                AccountLogLogic::AccountRecord($t_user_id, $two_order['money'], 1, AccountLog::distribution_inc_earnings, '', $two_order_id, $two_order['sn']);
                            }
                        }
                    }
                    // 插入一级代理订单
                 Db::name('agent_order')->strict(false)->insertGetId($first_order);
                }
            }
            Db::commit();
        }catch (\Exception $e ){
            Db::rollback();
            Db::name('log')->strict(false)->insert(['type' => 'error', 'log' => $e->getMessage()]);
        }
    }



    /**
     * @notes 集采联盟保证金
     * @param $order_sn
     * @param array $extra
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     * <AUTHOR>
     * @date 2021/7/13 6:33 下午
     */
    private static function bondcharge($order_sn, $extra = [])
    {
        try{
            Db::startTrans();
            $recharge_order = new ShopDeposit();
            $order = $recharge_order->where(['order_sn' => $order_sn])->find()->toArray();
            $update_data['pay_status'] = PayEnum::ISPAID;
            $update_data['payment_date'] = date('Y-m-d H:i:s');

            // 保存微信支付交易号
            if (isset($extra['transaction_id'])) {
                $update_data['transaction_id'] = $extra['transaction_id'];
                // 记录日志
                Db::name('log')->strict(false)->insert([
                    'type' => 'bondcharge_transaction_id',
                    'log' => json_encode([
                        'order_sn' => $order_sn,
                        'transaction_id' => $extra['transaction_id']
                    ]),
                    'creat_time' => date('Y-m-d H:i:s')
                ]);
            }

            $recharge_order->where(['id' => $order['id']])->update($update_data);
            $user2['jcshop_vip']=1;
            Db::name('shop')->where(['id' => $order['shop_id']])->update($user2);
            Db::commit();
        }catch (\Exception $e ){
            Db::rollback();
            Db::name('log')->strict(false)->insert(['type' => 'error', 'log' => $e->getMessage()]);
        }
    }

    /**
     * @notes 代理费回调
     * @param $order_sn
     * @param array $extra
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     * <AUTHOR>
     * @date 2021/7/13 6:33 下午
     */
    private static function agent($order_sn, $extra = [])
    {

        // 记录开始处理代理支付回调
        Db::name('log')->insert(['type'=>'agent_debug','log'=>'开始处理代理支付回调，订单号：'.$order_sn,'creat_time'=>date('Y-m-d H:i:s')]);

        try {
            $order = AgentMerchantfees::where('order_sn', $order_sn)->find();
            if (!$order) {
                Db::name('log')->insert(['type'=>'agent_error','log'=>'保证金订单不存在，订单号：'.$order_sn,'creat_time'=>date('Y-m-d H:i:s')]);
                throw new Exception('保证金订单不存在');
            }

            // 记录订单信息
            Db::name('log')->insert(['type'=>'agent_debug','log'=>'查询到订单信息：'.json_encode($order->toArray()),'creat_time'=>date('Y-m-d H:i:s')]);

            if ($order->status != 0) { // 0未支付
                Db::name('log')->insert(['type'=>'agent_error','log'=>'保证金订单状态异常或已支付，状态：'.$order->status,'creat_time'=>date('Y-m-d H:i:s')]);
                throw new Exception('保证金订单状态异常或已支付');
            }
            // 获取保证金设置
            $paymentTime = time();
            // 更新订单状态
            $order->status = 1; // 1已支付(公示期)
            $order->payment_date = $paymentTime;
            $order->transaction_id = $extra['transaction_id'];
            $order->old_money=Db::name('config')->where('name','agent_deposit_amount')->value('value');
            $saveResult = $order->save();
            if (!$saveResult) {
                Db::name('log')->insert(['type'=>'agent_error','log'=>'更新订单状态失败','creat_time'=>date('Y-m-d H:i:s')]);
                throw new Exception('更新订单状态失败');
            }
            Db::name('log')->insert(['type'=>'agent_debug','log'=>'订单状态更新成功','creat_time'=>date('Y-m-d H:i:s')]);

            //api/pdf_processor/addSignature  调用这个接口返回合同
            $hetong = [];
            if(!empty($order['qian_url'])){
 // 调用PDF处理接口添加签名和日期
            try {
                // 准备请求参数
                $postData = [
                    'signature_url' => $order['qian_url'], // 使用订单中的签名URL
                    'date_format' => 'Y年m月d日' // 使用中文日期格式
                ];

                // 构建请求URL
                $apiUrl = request()->domain() . '/api/pdf_processor/addSignature';

                // 使用CURL发送POST请求
                $ch = curl_init();
                curl_setopt($ch, CURLOPT_URL, $apiUrl);
                curl_setopt($ch, CURLOPT_POST, 1);
                curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($postData));
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

                // 执行请求并获取响应
                $response = curl_exec($ch);
                $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                curl_close($ch);

                // 记录请求日志
                Db::name('log')->insert([
                    'type' => 'agent_pdf_process',
                    'log' => '调用PDF处理接口，参数：' . json_encode($postData) . '，响应：' . $response,
                    'creat_time' => date('Y-m-d H:i:s')
                ]);

                // 解析响应
                if ($httpCode == 200) {
                    $result = json_decode($response, true);
                    if ($result && isset($result['code']) && $result['code'] == 1 && isset($result['data']['file_url'])) {
                        $hetong['file_url'] = $result['data']['file_url'];
                        Db::name('log')->insert([
                            'type' => 'agent_pdf_success',
                            'log' => '成功生成代理合同，URL：' . $hetong['file_url'],
                            'creat_time' => date('Y-m-d H:i:s')
                        ]);
                    } else {
                        Db::name('log')->insert([
                            'type' => 'agent_pdf_error',
                            'log' => '解析PDF处理接口响应失败：' . $response,
                            'creat_time' => date('Y-m-d H:i:s')
                        ]);
                    }
                } else {
                    Db::name('log')->insert([
                        'type' => 'agent_pdf_error',
                        'log' => 'PDF处理接口请求失败，HTTP状态码：' . $httpCode,
                        'creat_time' => date('Y-m-d H:i:s')
                    ]);
                }
            } catch (\Exception $e) {
                Db::name('log')->insert([
                    'type' => 'agent_pdf_error',
                    'log' => 'PDF处理接口异常：' . $e->getMessage(),
                    'creat_time' => date('Y-m-d H:i:s')
                ]);
            }
            }
           

            // 创建代理记录
            $agentData = [
                'user_id' => $order->user_id,
                'area_id' => 0, // 默认值，根据实际情况调整
                'is_type' => 0, // 默认不是区域代理
                'is_freeze' => 0, // 默认不冻结
                'level' => 1, // 默认一级代理
                'parent_id' => 0, // 默认无上级
                'distribution_start_time' => $paymentTime,
                'distribution_end_time' => strtotime('+1 year', $paymentTime), // 默认一年有效期
                'agent_code' => 'AG'.date('YmdHis').rand(1000,9999),
                'create_time' => $paymentTime,
                'update_time' => $paymentTime,
                'buy_type' => '线上购买',
                'hetong_url'=> $hetong['file_url'] ?? ''
            ];

            $agentId = Db::name('agent')->insertGetId($agentData);
            // 成为代理
            $updateResult = User::where('id', $order->user_id)->update(['is_agent' => 1,'agent_id'=>$agentId]);

            if (!$updateResult) {
                Db::name('log')->insert(['type'=>'agent_error','log'=>'更新用户代理状态失败，用户ID：'.$order->user_id,'creat_time'=>date('Y-m-d H:i:s')]);
                throw new Exception('更新用户代理状态失败');
            }
            Db::name('log')->insert(['type'=>'agent_debug','log'=>'代理支付回调处理完成','creat_time'=>date('Y-m-d H:i:s')]);
        } catch (Exception $e) {

            Db::name('log')->insert(['type'=>'agent_error','log'=>'代理支付回调处理异常：'.$e->getMessage(),'creat_time'=>date('Y-m-d H:i:s')]);
            throw $e; // 重新抛出异常，让上层捕获
        }
    }




  /*
   * 广告费回调
   */

    private static function AdOrder($order_sn, $extra = []){

        try{
            Db::startTrans();
            $recharge_order = new AdOrder();
            $order = $recharge_order->where(['order_sn' => $order_sn])->find()->toArray();
            $update_data['status'] =1;
            $update_data['start_time'] = time();
            $update_data['end_time'] = time()+$order['buy_time'];
            $recharge_order->where('id' ,$order['id'])->update($update_data);
            //广告位已出售数量加1
            Db::name('ad_position')->where('id',$order['ad_position_id'])->inc('ad_buys', $order['ad_buynums'])->update();
            Db::commit();
        }catch (\Exception $e ){
            Db::rollback();
        }
    }

    /**
     * @notes 商家保证金订单明细回调
     * @param string $detail_sn 保证金明细单号
     * @param array $extra 支付回调附加参数
     * @throws Exception
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    private static function deposit_detail($detail_sn, $extra = [])
    {
        $deposit_detail = ShopDepositDetails::where('sn', $detail_sn)->find();
        if (!$deposit_detail) {
            throw new Exception('保证金明细记录不存在: ' . $detail_sn);
        }
        if ($deposit_detail->pay_status == PayEnum::ISPAID) {
            // 已处理过，直接返回成功
            return true;
        }

        // 更新保证金明细状态
        $deposit_detail->pay_status = PayEnum::ISPAID;
        $deposit_detail->pay_time = time();
        if (isset($extra['transaction_id'])) {
            $deposit_detail->transaction_id = $extra['transaction_id'];
        }
        $detail_update_result = $deposit_detail->save();
        if (!$detail_update_result) {
            throw new Exception('更新保证金明细状态失败: ' . $detail_sn);
        }

        // 更新总保证金记录
        $deposit = ShopDeposit::find($deposit_detail->deposit_id);
        if (!$deposit) {
             throw new Exception('关联的总保证金记录不存在, deposit_id: ' . $deposit_detail->deposit_id);
        }

        if ($deposit_detail->change_type == 1) { // 首次缴纳
            $deposit->status = 1; // 更新状态为已缴纳
            // deposit_amount 在创建时已设置，这里无需修改
        } elseif ($deposit_detail->change_type == 2) { // 增加/补缴
            $deposit->deposit_amount = ['inc', $deposit_detail->amount]; // 增加金额
        } else {
            // 其他类型 change_type (如扣除、退还) 不应通过支付回调处理
            Log::warning('保证金支付回调处理了非缴纳/增加类型的明细: ' . $detail_sn . ', change_type: ' . $deposit_detail->change_type);
            // 可以选择抛出异常或仅记录日志
            // throw new Exception('保证金支付回调类型错误');
        }
        $deposit->update_time = time();
        $deposit_update_result = $deposit->save();
        if (!$deposit_update_result) {
            throw new Exception('更新总保证金记录失败, deposit_id: ' . $deposit_detail->deposit_id);
        }

        // 可选：发送通知给商家
        $shop = Shop::find($deposit->shop_id);
        if ($shop && $shop->mobile) {
             event('Notice', [
                 'scene' => $deposit_detail->change_type == 1 ? NoticeEnum::SHOP_DEPOSIT_PAID_NOTICE : NoticeEnum::SHOP_DEPOSIT_REPLENISH_NOTICE, // 根据类型选择不同通知场景
                 'mobile' => $shop->mobile,
                 'params' => [
                     'amount' => $deposit_detail->amount,
                     'total_deposit' => $deposit->deposit_amount // 传递更新后的总额
                     // ... 其他可能需要的参数
                 ]
             ]);
        }
    }

    /**
     * @notes 代理保证金明细回调
     * @param string $detail_sn 代理保证金明细单号
     * @param array $extra 支付回调附加参数
     * @throws Exception
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    private static function agent_deposit_detail($detail_sn, $extra = [])
    {
        // 记录开始处理代理保证金明细支付回调
        Db::name('log')->insert(['type'=>'agent_deposit_detail_debug','log'=>'开始处理代理保证金明细支付回调，单号：'.$detail_sn,'creat_time'=>date('Y-m-d H:i:s')]);

        try {
            // 查找代理保证金明细记录
            $deposit_detail = Db::name('agent_deposit_details')->where('sn', $detail_sn)->find();
            if (!$deposit_detail) {
                Db::name('log')->insert(['type'=>'agent_deposit_detail_error','log'=>'代理保证金明细记录不存在: '.$detail_sn,'creat_time'=>date('Y-m-d H:i:s')]);
                throw new Exception('代理保证金明细记录不存在: ' . $detail_sn);
            }

            if ($deposit_detail['pay_status'] == PayEnum::ISPAID) {
                // 已处理过，直接返回成功
                Db::name('log')->insert(['type'=>'agent_deposit_detail_debug','log'=>'代理保证金明细已支付，跳过处理: '.$detail_sn,'creat_time'=>date('Y-m-d H:i:s')]);
                return true;
            }

            $paymentTime = time();

            // 更新代理保证金明细状态
            $updateData = [
                'pay_status' => PayEnum::ISPAID,
                'pay_time' => $paymentTime,
                'transaction_id' => $extra['transaction_id'] ?? '',
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $updateResult = Db::name('agent_deposit_details')->where('id', $deposit_detail['id'])->update($updateData);
            if (!$updateResult) {
                Db::name('log')->insert(['type'=>'agent_deposit_detail_error','log'=>'更新代理保证金明细状态失败: '.$detail_sn,'creat_time'=>date('Y-m-d H:i:s')]);
                throw new Exception('更新代理保证金明细状态失败: ' . $detail_sn);
            }

            // 查找代理记录
            $agent = Db::name('agent')->where('user_id', $deposit_detail['user_id'])->find();
            if (!$agent) {
                Db::name('log')->insert(['type'=>'agent_deposit_detail_error','log'=>'代理记录不存在，用户ID: '.$deposit_detail['user_id'],'creat_time'=>date('Y-m-d H:i:s')]);
                throw new Exception('代理记录不存在，用户ID: ' . $deposit_detail['user_id']);
            }

            // 更新代理有效期
            $updateAgentData = [
                'distribution_start_time' => $paymentTime,
                'distribution_end_time' => strtotime('+1 year', $paymentTime),
                'update_time' => $paymentTime,
                'is_freeze' => 0 // 确保代理状态为正常
            ];

            $updateAgentResult = Db::name('agent')->where('id', $agent['id'])->update($updateAgentData);
            if (!$updateAgentResult) {
                Db::name('log')->insert(['type'=>'agent_deposit_detail_error','log'=>'更新代理记录失败，代理ID: '.$agent['id'],'creat_time'=>date('Y-m-d H:i:s')]);
                throw new Exception('更新代理记录失败，代理ID: ' . $agent['id']);
            }

            // 确保用户的代理状态正确
            $user = User::where('id', $deposit_detail['user_id'])->find();
            if (!$user['is_agent'] || $user['agent_id'] != $agent['id']) {
                User::where('id', $deposit_detail['user_id'])->update([
                    'is_agent' => 1,
                    'agent_id' => $agent['id']
                ]);
            }

            // 发送通知
            if ($user && $user['mobile']) {
                event('Notice', [
                    'scene' => NoticeEnum::AGENT_REPLENISH_NOTICE,
                    'mobile' => $user['mobile'],
                    'params' => [
                        'amount' => $deposit_detail['amount'],
                        'end_time' => date('Y-m-d H:i:s', strtotime('+1 year', $paymentTime))
                    ]
                ]);
            }

            Db::name('log')->insert(['type'=>'agent_deposit_detail_debug','log'=>'代理保证金明细支付回调处理完成: '.$detail_sn,'creat_time'=>date('Y-m-d H:i:s')]);

        } catch (Exception $e) {
            Db::name('log')->insert(['type'=>'agent_deposit_detail_error','log'=>'代理保证金明细支付回调处理异常: '.$e->getMessage(),'creat_time'=>date('Y-m-d H:i:s')]);
            throw $e; // 重新抛出异常，让上层捕获
        }
    }

    /**
     * @notes 商家等级升级支付回调
     * @param $order_sn
     * @param array $extra
     * @throws \think\exception\PDOException
     */
    public static function ShopTierUpgrade($order_sn, $extra = [])
    {
        // 查找订单
        $order = \app\common\model\shop\ShopMerchantfees::where('order_sn', $order_sn)->find();
        if (!$order) {
            throw new \Exception('商家等级升级订单不存在');
        }

        if ($order->status == 1) {
            // 订单已支付，直接返回
            return true;
        }

        // 更新订单状态
        $order->status = 1;
        $order->payment_date = time();
        if (isset($extra['transaction_id'])) {
            $order->transaction_id = $extra['transaction_id'];
        }
        $order->save();

        // 如果是升级订单，更新商家等级
        if ($order->tier_type == ShopTierEnum::TYPE_UPGRADE && $order->shop_id > 0) {
            $shop = \app\common\model\shop\Shop::find($order->shop_id);
            if ($shop) {
                $shop->tier_level = $order->tier_level;
                $shop->tier_expire_time = time() + (365 * 24 * 3600); // 1年后到期
                $shop->save();

                // 记录升级日志
                Db::name('log')->insert([
                    'type' => 'tier_upgrade',
                    'log' => "商家ID:{$shop->id} 等级升级到:{$order->tier_level}",
                    'creat_time' => date('Y-m-d H:i:s')
                ]);
            }
        }

        return true;
    }

    /**
     * 采购套餐支付回调处理
     * @param $order_sn
     * @param array $extra
     * @throws \Exception
     */
    private static function PurchaserPackage($order_sn, $extra = [])
    {
        try {
            Db::startTrans();

            // 获取采购套餐订单
            $packageOrder = \app\common\model\PurchaserPackageOrder::where(['order_sn' => $order_sn])->find();
            if (!$packageOrder) {
                throw new \Exception('采购套餐订单不存在：' . $order_sn);
            }

            // 检查是否已支付
            if ($packageOrder['pay_status'] == 1) {
                Db::rollback();
                return; // 已支付，直接返回
            }

            // 更新订单支付状态
            $updateData = [
                'pay_status' => 1,
                'pay_time' => time(),
                'update_time' => time()
            ];

            if (isset($extra['transaction_id'])) {
                $updateData['transaction_id'] = $extra['transaction_id'];
            }

            $packageOrder->save($updateData);

            // 分配采购人员
            self::allocatePurchasers($packageOrder);

            Db::commit();

            // 记录支付成功日志
            Db::name('log')->insert([
                'type' => 'purchaser_package_payment_success',
                'log' => "采购套餐支付成功，商家ID:{$packageOrder['shop_id']}, 订单号:{$order_sn}, 金额:{$packageOrder['price']}, 分配人数:{$packageOrder['purchaser_count']}",
                'creat_time' => date('Y-m-d H:i:s')
            ]);

        } catch (\Exception $e) {
            Db::rollback();

            // 记录错误日志
            Db::name('log')->insert([
                'type' => 'purchaser_package_payment_error',
                'log' => "采购套餐支付回调处理失败，订单号:{$order_sn}, 错误信息:{$e->getMessage()}",
                'creat_time' => date('Y-m-d H:i:s')
            ]);

            throw $e;
        }
    }

    /**
     * 分配采购人员
     * @param $packageOrder
     * @throws \Exception
     */
    private static function allocatePurchasers($packageOrder)
    {
        // 获取商家信息
        $shop = \app\common\model\shop\Shop::find($packageOrder['shop_id']);
        if (!$shop) {
            throw new \Exception('商家不存在');
        }

        // 获取商家等级
        $shopLevel = $shop['level'] ?? 0; // 0=0元入驻, 1=商家会员, 2=实力厂商

        // 根据商家等级确定可分配的采购人员等级
        $allowedPurchaserLevels = [];
        switch ($shopLevel) {
            case 0: // 0元入驻
                $allowedPurchaserLevels = [1]; // 只能分配初级采购
                break;
            case 1: // 商家会员
                $allowedPurchaserLevels = [1, 2]; // 可分配初级、中级采购
                break;
            case 2: // 实力厂商
                $allowedPurchaserLevels = [1, 2, 3]; // 可分配所有等级采购
                break;
            default:
                $allowedPurchaserLevels = [1];
                break;
        }

        // 获取可用的采购人员（未分配或分配数量未满的）
        $availablePurchasers = Db::name('purchaser')
            ->where('status', 1) // 启用状态
            ->where('level', 'in', $allowedPurchaserLevels)
            ->where(function($query) {
                $query->whereNull('max_shops')
                      ->whereOr('assigned_shops', '<', Db::raw('max_shops'));
            })
            ->orderRaw('level ASC, assigned_shops ASC') // 优先分配等级低的、分配数量少的
            ->limit($packageOrder['purchaser_count'])
            ->select();

        if (count($availablePurchasers) < $packageOrder['purchaser_count']) {
            throw new \Exception('可用采购人员不足，需要' . $packageOrder['purchaser_count'] . '人，可用' . count($availablePurchasers) . '人');
        }

        // 分配采购人员
        $currentTime = time();
        $expireTime = strtotime('+1 year', $currentTime); // 分配有效期1年

        foreach ($availablePurchasers as $purchaser) {
            // 创建分配记录
            $allocationData = [
                'shop_id' => $packageOrder['shop_id'],
                'purchaser_id' => $purchaser['id'],
                'package_order_id' => $packageOrder['id'],
                'allocated_time' => $currentTime,
                'expire_time' => $expireTime,
                'status' => 1, // 有效
                'create_time' => $currentTime,
                'update_time' => $currentTime
            ];

            Db::name('purchaser_allocation')->insert($allocationData);

            // 更新采购人员的分配数量
            Db::name('purchaser')
                ->where('id', $purchaser['id'])
                ->inc('assigned_shops', 1)
                ->update(['update_time' => $currentTime]);
        }

        // 记录分配日志
        Db::name('log')->insert([
            'type' => 'purchaser_allocation_success',
            'log' => "采购人员分配成功，商家ID:{$packageOrder['shop_id']}, 分配人数:{$packageOrder['purchaser_count']}, 采购人员ID:" . implode(',', array_column($availablePurchasers, 'id')),
            'creat_time' => date('Y-m-d H:i:s')
        ]);
    }
}