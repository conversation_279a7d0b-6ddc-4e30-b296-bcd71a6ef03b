<?php
/**
 * 进采购群功能测试脚本
 * 访问地址：http://你的域名/test_purchase_group.php
 */

// 引入框架
require_once __DIR__ . '/../vendor/autoload.php';

use app\common\server\WeChatServer;
use EasyWeChat\Factory;
use EasyWeChat\Kernel\Messages\Text;
use EasyWeChat\Kernel\Messages\Image;

?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>进采购群功能测试</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .test-section { margin-bottom: 30px; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
        button { background: #007cba; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; }
        button:hover { background: #005a87; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 4px; overflow-x: auto; }
        input[type="text"] { width: 300px; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
    </style>
</head>
<body>
    <h1>进采购群功能测试</h1>
    
    <?php if ($_SERVER['REQUEST_METHOD'] === 'POST'): ?>
        
        <?php
        $action = $_POST['action'] ?? '';
        
        try {
            // 初始化ThinkPHP环境
            define('APP_PATH', __DIR__ . '/../app/');
            $app = new think\App();
            $app->initialize();
            
            switch ($action) {
                case 'test_config':
                    echo '<div class="test-section info">';
                    echo '<h3>配置检查结果</h3>';
                    
                    $config = WeChatServer::getOaConfig();
                    echo '<p><strong>APP ID:</strong> ' . ($config['app_id'] ?? '未配置') . '</p>';
                    echo '<p><strong>Secret:</strong> ' . (!empty($config['secret']) ? '已配置' : '未配置') . '</p>';
                    echo '<p><strong>Token:</strong> ' . ($config['token'] ?? '未配置') . '</p>';
                    
                    // 测试access_token
                    try {
                        $app = Factory::officialAccount($config);
                        $token = $app->access_token->getToken();
                        echo '<p><strong>Access Token:</strong> ' . (isset($token['access_token']) ? '获取成功' : '获取失败') . '</p>';
                    } catch (Exception $e) {
                        echo '<p><strong>Access Token:</strong> 获取失败 - ' . $e->getMessage() . '</p>';
                    }
                    
                    echo '</div>';
                    break;
                    
                case 'test_upload':
                    echo '<div class="test-section">';
                    echo '<h3>图片上传测试结果</h3>';
                    
                    // 检查图片文件
                    $imagePath = __DIR__ . '/uploads/qrcode/purchase_group_qr.jpg';
                    if (!file_exists($imagePath)) {
                        echo '<div class="error">图片文件不存在: ' . $imagePath . '</div>';
                        echo '<p>请确保已上传二维码图片到正确位置</p>';
                    } else {
                        echo '<p><strong>图片路径:</strong> ' . $imagePath . '</p>';
                        echo '<p><strong>文件大小:</strong> ' . filesize($imagePath) . ' bytes</p>';
                        
                        try {
                            $config = WeChatServer::getOaConfig();
                            $app = Factory::officialAccount($config);
                            
                            $result = $app->media->upload('image', $imagePath);
                            
                            if (isset($result['media_id'])) {
                                echo '<div class="success">';
                                echo '<p><strong>上传成功!</strong></p>';
                                echo '<p><strong>Media ID:</strong> ' . $result['media_id'] . '</p>';
                                echo '<p><strong>类型:</strong> ' . $result['type'] . '</p>';
                                echo '<p><strong>创建时间:</strong> ' . date('Y-m-d H:i:s', $result['created_at']) . '</p>';
                                echo '</div>';
                            } else {
                                echo '<div class="error">上传失败: ' . json_encode($result) . '</div>';
                            }
                        } catch (Exception $e) {
                            echo '<div class="error">上传异常: ' . $e->getMessage() . '</div>';
                        }
                    }
                    
                    echo '</div>';
                    break;
                    
                case 'test_send':
                    $openid = $_POST['openid'] ?? '';
                    if (empty($openid)) {
                        echo '<div class="error">请输入OpenID</div>';
                        break;
                    }
                    
                    echo '<div class="test-section">';
                    echo '<h3>消息发送测试结果</h3>';
                    echo '<p><strong>目标用户:</strong> ' . $openid . '</p>';
                    
                    try {
                        $config = WeChatServer::getOaConfig();
                        $app = Factory::officialAccount($config);
                        
                        // 发送文本消息
                        $textContent = '欢迎加入我们的采购群！我们有专业的采购团队为您提供优质的商品和服务。请扫描下方二维码进群，享受更多采购优惠和专业指导。';
                        $textResult = $app->customer_service->message(new Text($textContent))->to($openid)->send();
                        echo '<p><strong>文本消息发送结果:</strong> ' . json_encode($textResult) . '</p>';
                        
                        // 发送图片消息
                        $imagePath = __DIR__ . '/uploads/qrcode/purchase_group_qr.jpg';
                        if (file_exists($imagePath)) {
                            $uploadResult = $app->media->upload('image', $imagePath);
                            if (isset($uploadResult['media_id'])) {
                                $imageResult = $app->customer_service->message(new Image($uploadResult['media_id']))->to($openid)->send();
                                echo '<p><strong>图片消息发送结果:</strong> ' . json_encode($imageResult) . '</p>';
                                echo '<div class="success">两条消息发送完成！请检查微信接收情况。</div>';
                            } else {
                                echo '<div class="error">图片上传失败，无法发送图片消息</div>';
                            }
                        } else {
                            echo '<div class="error">图片文件不存在，无法发送图片消息</div>';
                        }
                        
                    } catch (Exception $e) {
                        echo '<div class="error">发送异常: ' . $e->getMessage() . '</div>';
                    }
                    
                    echo '</div>';
                    break;
            }
            
        } catch (Exception $e) {
            echo '<div class="test-section error">';
            echo '<h3>测试失败</h3>';
            echo '<p>错误信息: ' . $e->getMessage() . '</p>';
            echo '</div>';
        }
        
    endif; ?>
    
    <!-- 测试表单 -->
    <div class="test-section">
        <h3>1. 配置检查</h3>
        <p>检查微信公众号配置是否正确</p>
        <form method="post">
            <input type="hidden" name="action" value="test_config">
            <button type="submit">检查配置</button>
        </form>
    </div>
    
    <div class="test-section">
        <h3>2. 图片上传测试</h3>
        <p>测试将二维码图片上传到微信服务器</p>
        <form method="post">
            <input type="hidden" name="action" value="test_upload">
            <button type="submit">测试上传</button>
        </form>
    </div>
    
    <div class="test-section">
        <h3>3. 消息发送测试</h3>
        <p>测试发送完整的进采购群消息（文本+图片）</p>
        <form method="post">
            <input type="hidden" name="action" value="test_send">
            <p>
                <label>测试用户OpenID: </label>
                <input type="text" name="openid" placeholder="请输入真实的用户OpenID" required>
            </p>
            <button type="submit">发送测试消息</button>
        </form>
        <p><small>注意：请使用真实关注了公众号的用户OpenID进行测试</small></p>
    </div>
    
    <div class="test-section info">
        <h3>使用说明</h3>
        <ol>
            <li>首先运行"配置检查"确保微信配置正确</li>
            <li>然后运行"图片上传测试"确保图片能正常上传</li>
            <li>最后使用真实用户OpenID进行"消息发送测试"</li>
            <li>如果测试成功，说明功能正常，可以在微信公众号中使用"进采购群"关键词测试</li>
        </ol>
    </div>
    
</body>
</html>
